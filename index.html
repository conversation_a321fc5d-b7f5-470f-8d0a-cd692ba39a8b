<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8">
    <title>MÁY HÀN ĐIỆN CẦM TAY THÔNG MINH</title>
    <meta http-equiv="Cache-Control" content="no-cache">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta http-equiv="Expires" content="-1">
    <meta name="keywords" content="">
    <meta name="description" content="MÁY HÀN ĐIỆN CẦM TAY THÔNG MINH">
    <meta id='viewport' name='viewport' content='width=device-width, initial-scale=1' />
    <script type='text/javascript'>
      window.ladi_viewport = function(keyWidth) {
        var screen_width = window.ladi_screen_width || window.screen.width;
        keyWidth = keyWidth ? keyWidth : "innerWidth";
        var width = window[keyWidth] > 0 ? window[keyWidth] : screen_width;
        var widthDevice = width;
        var is_desktop = false;
        var content = "";
        if (typeof window.ladi_is_desktop == "undefined" || window.ladi_is_desktop == undefined) {
          window.ladi_is_desktop = is_desktop;
        }
        if (!is_desktop) {
          widthDevice = 420;
        } else {
          widthDevice = 960;
        }
        content = "width=" + widthDevice + ", user-scalable=no";
        var scale = 1;
        if (!is_desktop && widthDevice != screen_width && screen_width > 0) {
          scale = screen_width / widthDevice;
        }
        if (scale != 1) {
          content += ", initial-scale=" + scale + ", minimum-scale=" + scale + ", maximum-scale=" + scale;
        }
        var docViewport = document.getElementById("viewport");
        if (!docViewport) {
          docViewport = document.createElement("meta");
          docViewport.setAttribute("id", "viewport");
          docViewport.setAttribute("name", "viewport");
          document.head.appendChild(docViewport);
        }
        docViewport.setAttribute("content", content);
      };
      window.ladi_viewport();
      window.ladi_fbq_data = [];
      window.ladi_fbq = function() {
        window.ladi_fbq_data.push(arguments);
      };
      window.ladi_ttq_data = [];
      window.ladi_ttq = function() {
        window.ladi_ttq_data.push(arguments);
      };
    </script>
    <link rel="canonical" href="https://www.cokhitmaxtech.shop/may-han-dien" />
    <meta property="og:url" content="https://www.cokhitmaxtech.shop/may-han-dien" />
    <meta property="og:title" content="MÁY HÀN ĐIỆN CẦM TAY THÔNG MINH" />
    <meta property="og:type" content="website" />
    <meta property="og:image" content="https://static.ladipage.net/5dbe4694ed94dc587f3c244b/siucap1-removebg-20230331021854-qhjsw-20230523081924-bhfwj.png">
    <meta property="og:description" content="MÁY HÀN ĐIỆN CẦM TAY THÔNG MINH" />
    <meta name="format-detection" content="telephone=no" />
    <link rel="shortcut icon" href="https://static.ladipage.net/5dbe4694ed94dc587f3c244b/siucap1-removebg-20230331021854-qhjsw-20230523081924-bhfwj.png" />
    <link rel="dns-prefetch">
    <link rel="preconnect" href="https://fonts.googleapis.com/" crossorigin>
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link rel="preconnect" href="https://w.ladicdn.com/" crossorigin>
    <link rel="preconnect" href="https://s.ladicdn.com/" crossorigin>
    <link rel="preconnect" href="https://api.ldpform.com/" crossorigin>
    <link rel="preconnect" href="https://a.ladipage.com/" crossorigin>
    <link rel="preconnect" href="https://api.sales.ldpform.net/" crossorigin>
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&family=Dancing+Script:wght@400;700&family=Tinos:wght@400;700&family=Quicksand:wght@400;700&family=Montserrat:wght@400;700&family=Roboto:wght@400;700&family=Roboto+Slab:wght@400;700&family=Oswald:wght@400;700&display=swap" as="style" onload="this.onload = null; this.rel = 'stylesheet';">
    <link rel="preload" href="https://w.ladicdn.com/v2/source/ladipagev3.min.js?v=1695116904561" as="script">
    <style id="style_ladi" type="text/css">
      a,
      abbr,
      acronym,
      address,
      applet,
      article,
      aside,
      audio,
      b,
      big,
      blockquote,
      body,
      button,
      canvas,
      caption,
      center,
      cite,
      code,
      dd,
      del,
      details,
      dfn,
      div,
      dl,
      dt,
      em,
      embed,
      fieldset,
      figcaption,
      figure,
      footer,
      form,
      h1,
      h2,
      h3,
      h4,
      h5,
      h6,
      header,
      hgroup,
      html,
      i,
      iframe,
      img,
      input,
      ins,
      kbd,
      label,
      legend,
      li,
      mark,
      menu,
      nav,
      object,
      ol,
      output,
      p,
      pre,
      q,
      ruby,
      s,
      samp,
      section,
      select,
      small,
      span,
      strike,
      strong,
      sub,
      summary,
      sup,
      table,
      tbody,
      td,
      textarea,
      tfoot,
      th,
      thead,
      time,
      tr,
      tt,
      u,
      ul,
      var,
      video {
        margin: 0;
        padding: 0;
        border: 0;
        outline: 0;
        font-size: 100%;
        font: inherit;
        vertical-align: baseline;
        box-sizing: border-box;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale
      }

      article,
      aside,
      details,
      figcaption,
      figure,
      footer,
      header,
      hgroup,
      menu,
      nav,
      section {
        display: block
      }

      body {
        line-height: 1
      }

      a {
        text-decoration: none
      }

      ol,
      ul {
        list-style: none
      }

      blockquote,
      q {
        quotes: none
      }

      blockquote:after,
      blockquote:before,
      q:after,
      q:before {
        content: '';
        content: none
      }

      table {
        border-collapse: collapse;
        border-spacing: 0
      }

      .ladi-loading {
        z-index: 900000000000;
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-color: rgba(0, 0, 0, .1)
      }

      .ladi-loading .loading {
        width: 80px;
        height: 80px;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        overflow: hidden;
        position: absolute
      }

      .ladi-loading .loading div {
        position: absolute;
        width: 6px;
        height: 6px;
        background: #fff;
        border-radius: 50%;
        animation: ladi-loading 1.2s linear infinite
      }

      .ladi-loading .loading div:nth-child(1) {
        animation-delay: 0s;
        top: 37px;
        left: 66px
      }

      .ladi-loading .loading div:nth-child(2) {
        animation-delay: -.1s;
        top: 22px;
        left: 62px
      }

      .ladi-loading .loading div:nth-child(3) {
        animation-delay: -.2s;
        top: 11px;
        left: 52px
      }

      .ladi-loading .loading div:nth-child(4) {
        animation-delay: -.3s;
        top: 7px;
        left: 37px
      }

      .ladi-loading .loading div:nth-child(5) {
        animation-delay: -.4s;
        top: 11px;
        left: 22px
      }

      .ladi-loading .loading div:nth-child(6) {
        animation-delay: -.5s;
        top: 22px;
        left: 11px
      }

      .ladi-loading .loading div:nth-child(7) {
        animation-delay: -.6s;
        top: 37px;
        left: 7px
      }

      .ladi-loading .loading div:nth-child(8) {
        animation-delay: -.7s;
        top: 52px;
        left: 11px
      }

      .ladi-loading .loading div:nth-child(9) {
        animation-delay: -.8s;
        top: 62px;
        left: 22px
      }

      .ladi-loading .loading div:nth-child(10) {
        animation-delay: -.9s;
        top: 66px;
        left: 37px
      }

      .ladi-loading .loading div:nth-child(11) {
        animation-delay: -1s;
        top: 62px;
        left: 52px
      }

      .ladi-loading .loading div:nth-child(12) {
        animation-delay: -1.1s;
        top: 52px;
        left: 62px
      }

      @keyframes ladi-loading {

        0%,
        100%,
        20%,
        80% {
          transform: scale(1)
        }

        50% {
          transform: scale(1.5)
        }
      }

      .ladipage-message {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: 10000000000;
        background: rgba(0, 0, 0, .3)
      }

      .ladipage-message .ladipage-message-box {
        width: 400px;
        max-width: calc(100% - 50px);
        height: 160px;
        border: 1px solid rgba(0, 0, 0, .3);
        background-color: #fff;
        position: fixed;
        top: calc(50% - 155px);
        left: 0;
        right: 0;
        margin: auto;
        border-radius: 10px
      }

      .ladipage-message .ladipage-message-box span {
        display: block;
        background-color: rgba(6, 21, 40, .05);
        color: #000;
        padding: 12px 15px;
        font-weight: 600;
        font-size: 16px;
        line-height: 16px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px
      }

      .ladipage-message .ladipage-message-box .ladipage-message-text {
        display: -webkit-box;
        font-size: 14px;
        padding: 0 20px;
        margin-top: 10px;
        line-height: 18px;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
        text-overflow: ellipsis
      }

      .ladipage-message .ladipage-message-box .ladipage-message-close {
        display: block;
        position: absolute;
        right: 15px;
        bottom: 10px;
        margin: 0 auto;
        padding: 10px 0;
        border: none;
        width: 80px;
        text-transform: uppercase;
        text-align: center;
        color: #000;
        background-color: #e6e6e6;
        border-radius: 5px;
        text-decoration: none;
        font-size: 14px;
        line-height: 14px;
        font-weight: 600;
        cursor: pointer
      }

      .lightbox-screen {
        display: none;
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
        z-index: 9000000080;
        background: rgba(0, 0, 0, .5)
      }

      .lightbox-screen .lightbox-close {
        position: absolute;
        z-index: 9000000090;
        cursor: pointer
      }

      .lightbox-screen .lightbox-hidden {
        display: none
      }

      .lightbox-screen .lightbox-close {
        width: 16px;
        height: 16px;
        margin: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23fff%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M23.4144%202.00015L2.00015%2023.4144L0.585938%2022.0002L22.0002%200.585938L23.4144%202.00015Z%22%3E%3C%2Fpath%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M2.00015%200.585938L23.4144%2022.0002L22.0002%2023.4144L0.585938%202.00015L2.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E")
      }

      body {
        font-size: 12px;
        -ms-text-size-adjust: none;
        -moz-text-size-adjust: none;
        -o-text-size-adjust: none;
        -webkit-text-size-adjust: none;
        background-color: #fff;
      }

      .overflow-hidden {
        overflow: hidden;
      }

      .ladi-transition {
        transition: all 150ms linear 0s;
      }

      .opacity-0 {
        opacity: 0;
      }

      .height-0 {
        height: 0 !important;
      }

      .pointer-events-none {
        pointer-events: none;
      }

      .transition-parent-collapse-height {
        transition: height 150ms linear 0s;
      }

      .transition-parent-collapse-top {
        transition: top 150ms linear 0s;
      }

      .transition-readmore {
        transition: height 350ms linear 0s;
      }

      .transition-collapse {
        transition: height 150ms linear 0s;
      }

      body.grab {
        cursor: grab;
      }

      .ladi-wraper {
        width: 100%;
        min-height: 100%;
        overflow: hidden;
      }

      .ladi-container {
        position: relative;
        margin: 0 auto;
        height: 100%;
      }

      .ladi-element {
        position: absolute;
      }

      .ladi-overlay {
        position: absolute;
        top: 0;
        left: 0;
        height: 100%;
        width: 100%;
        pointer-events: none;
      }

      @media (hover: hover) {
        .ladi-check-hover {
          opacity: 0;
        }
      }

      .ladi-section {
        margin: 0 auto;
        position: relative;
      }

      .ladi-section[data-tab-id] {
        display: none;
      }

      .ladi-section.selected[data-tab-id] {
        display: block;
      }

      .ladi-section .ladi-section-background {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        pointer-events: none;
        overflow: hidden;
      }

      .ladi-gallery {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .ladi-gallery .ladi-gallery-view {
        position: absolute;
        overflow: hidden;
      }

      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        width: 100%;
        height: 100%;
        position: relative;
        display: none;
        transition: transform 500ms ease-in-out;
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
        -webkit-perspective: 1000px;
        perspective: 1000px;
      }

      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.play-video {
        cursor: pointer;
      }

      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.play-video:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        width: 60px;
        height: 60px;
        background: url(https://w.ladicdn.com/source/ladipage-play.svg?v=1.0) no-repeat center center;
        background-size: contain;
        pointer-events: none;
        cursor: pointer;
      }

      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.next,
      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.selected.right {
        left: 0;
        transform: translate3d(100%, 0, 0);
      }

      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.prev,
      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.selected.left {
        left: 0;
        transform: translate3d(-100%, 0, 0);
      }

      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.next.left,
      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.prev.right,
      .ladi-gallery .ladi-gallery-view>.ladi-gallery-view-item.selected {
        left: 0;
        transform: translate3d(0, 0, 0);
      }

      .ladi-gallery .ladi-gallery-view>.selected,
      .ladi-gallery .ladi-gallery-view>.next,
      .ladi-gallery .ladi-gallery-view>.prev {
        display: block;
      }

      .ladi-gallery .ladi-gallery-view>.selected {
        left: 0;
      }

      .ladi-gallery .ladi-gallery-view>.next,
      .ladi-gallery .ladi-gallery-view>.prev {
        position: absolute;
        top: 0;
        width: 100%;
      }

      .ladi-gallery .ladi-gallery-view>.next {
        left: 100%;
      }

      .ladi-gallery .ladi-gallery-view>.prev {
        left: -100%;
      }

      .ladi-gallery .ladi-gallery-view>.next.left,
      .ladi-gallery .ladi-gallery-view>.prev.right {
        left: 0;
      }

      .ladi-gallery .ladi-gallery-view>.selected.left {
        left: -100%;
      }

      .ladi-gallery .ladi-gallery-view>.selected.right {
        left: 100%;
      }

      .ladi-gallery .ladi-gallery-control {
        position: absolute;
        overflow: hidden;
      }

      .ladi-gallery .ladi-gallery-view .ladi-gallery-view-arrow {
        position: absolute;
        top: calc(50% - (33px) / 2);
        cursor: pointer;
        z-index: 90000040;
      }

      .ladi-gallery .ladi-gallery-view .ladi-gallery-view-arrow-left {
        left: 5px;
        transform: rotateY(180deg);
        -webkit-transform: rotateY(180deg);
      }

      .ladi-gallery .ladi-gallery-view .ladi-gallery-view-arrow-right {
        right: 5px;
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-arrow {
        position: absolute;
        cursor: pointer;
        z-index: 90000040;
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-box {
        position: relative;
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-box .ladi-gallery-control-item {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center center;
        float: left;
        position: relative;
        cursor: pointer;
        filter: invert(15%);
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-box .ladi-gallery-control-item.play-video:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
        width: 30px;
        height: 30px;
        background: url(https://w.ladicdn.com/source/ladipage-play.svg?v=1.0) no-repeat center center;
        background-size: contain;
        pointer-events: none;
        cursor: pointer;
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-box .ladi-gallery-control-item:hover {
        filter: none;
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-box .ladi-gallery-control-item.selected {
        filter: none;
      }

      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-box .ladi-gallery-control-item:last-child {
        margin-right: 0 !important;
        margin-bottom: 0 !important;
        ;
      }

      .ladi-gallery .ladi-gallery-view .ladi-gallery-view-arrow,
      .ladi-gallery .ladi-gallery-control .ladi-gallery-control-arrow {
        width: 33px;
        height: 33px;
        background-repeat: no-repeat;
        background-position: center center;
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23000%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M7.00015%200.585938L18.4144%2012.0002L7.00015%2023.4144L5.58594%2022.0002L15.5859%2012.0002L5.58594%202.00015L7.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
      }

      .ladi-gallery.ladi-gallery-bottom .ladi-gallery-view {
        top: 0;
        width: 100%;
      }

      .ladi-gallery.ladi-gallery-bottom .ladi-gallery-control {
        width: 100%;
        bottom: 0;
      }

      .ladi-gallery.ladi-gallery-bottom .ladi-gallery-control .ladi-gallery-control-arrow {
        top: calc(50% - (33px) / 2);
      }

      .ladi-gallery.ladi-gallery-bottom .ladi-gallery-control .ladi-gallery-control-arrow-left {
        left: 0px;
        transform: rotateY(180deg) scale(0.6);
        -webkit-transform: rotateY(180deg) scale(0.6);
      }

      .ladi-gallery.ladi-gallery-bottom .ladi-gallery-control .ladi-gallery-control-arrow-right {
        right: 0px;
        transform: scale(0.6);
        -webkit-transform: scale(0.6);
      }

      .ladi-gallery.ladi-gallery-bottom .ladi-gallery-control .ladi-gallery-control-box {
        display: -webkit-inline-flex;
        display: inline-flex;
        left: 0;
        transition: left 150ms ease-in-out;
      }

      .ladi-box {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .ladi-frame {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .ladi-frame .ladi-frame-background {
        height: 100%;
        width: 100%;
        pointer-events: none;
        transition: inherit;
      }

      #SECTION_POPUP .ladi-container {
        z-index: 90000070;
      }

      #SECTION_POPUP .ladi-container>.ladi-element {
        z-index: 90000070;
        position: fixed;
        display: none;
      }

      #SECTION_POPUP .ladi-container>.ladi-element[data-fixed-close="true"] {
        position: relative !important;
      }

      #SECTION_POPUP .ladi-container>.ladi-element.hide-visibility {
        display: block !important;
        visibility: hidden !important;
      }

      #SECTION_POPUP .popup-close {
        position: absolute;
        right: 0px;
        top: 0px;
        z-index: 9000000080;
        cursor: pointer;
        width: 16px;
        height: 16px;
        margin: 10px;
        background-repeat: no-repeat;
        background-position: center center;
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23000%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M23.4144%202.00015L2.00015%2023.4144L0.585938%2022.0002L22.0002%200.585938L23.4144%202.00015Z%22%3E%3C%2Fpath%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M2.00015%200.585938L23.4144%2022.0002L22.0002%2023.4144L0.585938%202.00015L2.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
      }

      .ladi-popup {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .ladi-popup .ladi-popup-background {
        height: 100%;
        width: 100%;
        pointer-events: none;
      }

      .ladi-countdown {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .ladi-countdown .ladi-countdown-background {
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
        display: table;
        pointer-events: none;
      }

      .ladi-countdown .ladi-countdown-text {
        position: absolute;
        width: 100%;
        height: 100%;
        text-decoration: inherit;
        display: table;
        pointer-events: none;
      }

      .ladi-countdown .ladi-countdown-text span {
        display: table-cell;
        vertical-align: middle;
      }

      .ladi-countdown>.ladi-element {
        text-decoration: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
        position: relative;
        display: inline-block;
      }

      .ladi-countdown>.ladi-element:last-child {
        margin-right: 0px !important;
      }

      .ladi-button {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .ladi-button:active {
        transform: translateY(2px);
        transition: transform 0.2s linear;
      }

      .ladi-button .ladi-button-background {
        height: 100%;
        width: 100%;
        pointer-events: none;
        transition: inherit;
      }

      .ladi-button>.ladi-button-headline,
      .ladi-button>.ladi-button-shape {
        width: 100% !important;
        height: 100% !important;
        top: 0 !important;
        left: 0 !important;
        display: table;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }

      .ladi-button>.ladi-button-shape .ladi-shape {
        margin: auto;
        top: 0;
        bottom: 0;
      }

      .ladi-button>.ladi-button-headline .ladi-headline {
        display: table-cell;
        vertical-align: middle;
      }

      .ladi-form {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .ladi-form>.ladi-element {
        text-transform: inherit;
        text-decoration: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form .ladi-button>.ladi-button-headline {
        color: initial;
        font-size: initial;
        font-weight: initial;
        text-transform: initial;
        text-decoration: initial;
        font-style: initial;
        text-align: initial;
        letter-spacing: initial;
        line-height: initial;
      }

      .ladi-form>.ladi-element .ladi-form-item-container {
        text-transform: inherit;
        text-decoration: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form>[data-quantity="true"] .ladi-form-item-container {
        overflow: hidden;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item {
        text-transform: inherit;
        text-decoration: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item-background {
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background-size: 9px 6px !important;
        background-position: right .5rem center;
        background-repeat: no-repeat;
        padding-right: 24px;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select-2 {
        width: calc(100% / 2 - 5px);
        max-width: calc(100% / 2 - 5px);
        min-width: calc(100% / 2 - 5px);
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select-2:nth-child(3) {
        margin-left: 7.5px;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select-3 {
        width: calc(100% / 3 - 5px);
        max-width: calc(100% / 3 - 5px);
        min-width: calc(100% / 3 - 5px);
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select-3:nth-child(3) {
        margin-left: 7.5px;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select-3:nth-child(4) {
        margin-left: 7.5px;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select option {
        color: initial;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control:not(.ladi-form-control-select) {
        text-transform: inherit;
        text-decoration: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select {
        text-transform: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-control-select:not([data-selected=""]) {
        text-decoration: inherit;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-checkbox-item {
        text-transform: inherit;
        text-decoration: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
        vertical-align: middle;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-checkbox-box-item {
        display: inline-block;
        width: fit-content;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-checkbox-item span {
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-checkbox-item span[data-checked="true"] {
        text-transform: inherit;
        text-decoration: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form>.ladi-element .ladi-form-item-container .ladi-form-item .ladi-form-checkbox-item span[data-checked="false"] {
        text-transform: inherit;
        text-align: inherit;
        letter-spacing: inherit;
        color: inherit;
        background-size: inherit;
        background-attachment: inherit;
        background-origin: inherit;
      }

      .ladi-form .ladi-form-item-container {
        position: absolute;
        width: 100%;
        height: 100%;
      }

      .ladi-form .ladi-form-item-title-value {
        font-weight: bold;
        word-break: break-word;
      }

      .ladi-form .ladi-form-label-container {
        position: relative;
        width: 100%;
      }

      .ladi-form .ladi-form-control-file {
        background-repeat: no-repeat;
        background-position: calc(100% - 5px) center;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item {
        display: inline-block;
        cursor: pointer;
        position: relative;
        border-radius: 0px !important;
        user-select: none;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.image {
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.no-value {
        display: none !important;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.text.disabled {
        opacity: 0.35;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.image.disabled {
        opacity: 0.2;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.color.disabled {
        opacity: 0.15;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.selected:before {
        content: '';
        width: 0;
        height: 0;
        bottom: -1px;
        right: -1px;
        position: absolute;
        border-width: 0 0 15px 15px;
        border-color: transparent;
        border-style: solid;
      }

      .ladi-form .ladi-form-label-container .ladi-form-label-item.selected:after {
        content: '';
        background-image: url("data:image/svg+xml;utf8,%3Csvg
xmlns='http://www.w3.org/2000/svg'enable-background='new 0 0 12 12'viewBox='0 0 12 12'x='0'fill='%23fff'y='0'%3E%3Cg%3E%3Cpath d='m5.2 10.9c-.2 0-.5-.1-.7-.2l-4.2-3.7c-.4-.4-.5-1-.1-1.4s1-.5 1.4-.1l3.4 3 5.1-7c .3-.4 1-.5 1.4-.2s.5 1 .2 1.4l-5.7 7.9c-.2.2-.4.4-.7.4 0-.1 0-.1-.1-.1z'%3E%3C/path%3E%3C/g%3E%3C/svg%3E");background-repeat: no-repeat;background-position: bottom right;width: 7px;height: 7px;bottom: 0;right: 0;position: absolute;}.ladi-form .ladi-form-item {width: 100%;height: 100%;position: absolute;}.ladi-form .ladi-form-item-background {position: absolute;width: 100%;height: 100%;top: 0;left: 0;pointer-events: none;}.ladi-form .ladi-form-item.ladi-form-checkbox {height: auto;}.ladi-form .ladi-form-item .ladi-form-control {background-color: transparent;min-width: 100%;min-height: 100%;max-width: 100%;max-height: 100%;width: 100%;height: 100%;padding: 0 5px;color: inherit;font-size: inherit;border: none;}.ladi-form .ladi-form-item.ladi-form-checkbox {padding: 0 5px;}.ladi-form .ladi-form-item.ladi-form-checkbox.ladi-form-checkbox-vertical .ladi-form-checkbox-item {margin-top: 0 !important;margin-left: 0 !important;margin-right: 0 !important;display: flex;align-items: center;border: none;}.ladi-form .ladi-form-item.ladi-form-checkbox.ladi-form-checkbox-horizontal .ladi-form-checkbox-item {margin-top: 0 !important;margin-left: 0 !important;margin-right: 10px !important;display: inline-flex;align-items: center;border: none;position: relative;}.ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item input {margin-right: 5px;display: block;}.ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item span {cursor: default;word-break: break-word;}.ladi-form .ladi-form-item textarea.ladi-form-control {resize: none;padding: 5px;}.ladi-form .ladi-button {cursor: pointer;}.ladi-form .ladi-button .ladi-headline {cursor: pointer;user-select: none;}.ladi-form .ladi-element .ladi-form-otp::-webkit-outer-spin-button, .ladi-form .ladi-element .ladi-form-otp::-webkit-inner-spin-button {-webkit-appearance: none;margin: 0;}.ladi-form .ladi-element .ladi-form-item .button-get-code {display: none;position: absolute;right: 0;top: 0;bottom: 0;margin: auto 0;line-height: initial;padding: 5px 10px;height: max-content;cursor: pointer;user-select: none;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;}.ladi-form .ladi-element .ladi-form-item .button-get-code.hide-visibility {display: block !important;visibility: hidden !important;}.ladi-form .ladi-form-item.otp-resend .button-get-code {display: block;}.ladi-form .ladi-form-item.otp-countdown:before {content: attr(data-countdown-time) "s";position: absolute;top: 0;bottom: 0;margin: auto 0;height: max-content;line-height: initial;}.ladi-form [data-variant="true"] select option[disabled] {background: #fff;color: #b8b8b8 !important;}.ladi-google-recaptcha-checkbox {position: absolute;display: inline-block;transform: translateY(-100%);margin-top: -5px;z-index: 90000010;}.ladi-video {position: absolute;width: 100%;height: 100%;cursor: pointer;overflow: hidden;}.ladi-video .ladi-video-background {position: absolute;width: 100%;height: 100%;top: 0;left: 0;pointer-events: none;}.button-unmute {cursor: pointer;position: absolute;width: 100%;height: 100%;top: 0;left: 0;right: 0;bottom: 0;margin: auto;}.button-unmute div {background-image: url("data:image/svg+xml; utf8, %3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2036%2036%22%20width%3D%22100%25%22%20height%3D%22100%25%22%20fill%3D%22%23fff%22%3E%3Cpath%20d%3D%22m%2021.48%2C17.98%20c%200%2C-1.77%20-1.02%2C-3.29%20-2.5%2C-4.03%20v%202.21%20l%202.45%2C2.45%20c%20.03%2C-0.2%20.05%2C-0.41%20.05%2C-0.63%20z%20m%202.5%2C0%20c%200%2C.94%20-0.2%2C1.82%20-0.54%2C2.64%20l%201.51%2C1.51%20c%20.66%2C-1.24%201.03%2C-2.65%201.03%2C-4.15%200%2C-4.28%20-2.99%2C-7.86%20-7%2C-8.76%20v%202.05%20c%202.89%2C.86%205%2C3.54%205%2C6.71%20z%20M%209.25%2C8.98%20l%20-1.27%2C1.26%204.72%2C4.73%20H%207.98%20v%206%20H%2011.98%20l%205%2C5%20v%20-6.73%20l%204.25%2C4.25%20c%20-0.67%2C.52%20-1.42%2C.93%20-2.25%2C1.18%20v%202.06%20c%201.38%2C-0.31%202.63%2C-0.95%203.69%2C-1.81%20l%202.04%2C2.05%201.27%2C-1.27%20-9%2C-9%20-7.72%2C-7.72%20z%20m%207.72%2C.99%20-2.09%2C2.08%202.09%2C2.09%20V%209.98%20z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");width: 60px;height: 60px;position: absolute;top: 0;left: 0;bottom: 0;right: 0;margin: auto;background-color: rgba(0, 0, 0, 0.5);border-radius: 100%;background-size: 90%;background-repeat: no-repeat;background-position: center center;}.ladi-group {position: absolute;width: 100%;height: 100%;}.ladi-shape {position: absolute;width: 100%;height: 100%;pointer-events: none;}.ladi-cart-number {position: absolute;top: -2px;right: -7px;background: #f36e36;text-align: center;width: 18px;height: 18px;line-height: 18px;font-size: 12px;font-weight: bold;color: #fff;border-radius: 100%;}.ladi-image {position: absolute;width: 100%;height: 100%;overflow: hidden;}.ladi-image .ladi-image-background {background-repeat: no-repeat;background-position: left top;background-size: cover;background-attachment: scroll;background-origin: content-box;position: absolute;margin: 0 auto;width: 100%;height: 100%;pointer-events: none;}.ladi-headline {width: 100%;display: inline-block;word-break: break-word;background-size: cover;background-position: center center;}.ladi-headline a {text-decoration: underline;}.ladi-paragraph {width: 100%;display: inline-block;word-break: break-word;}.ladi-paragraph a {text-decoration: underline;}.ladi-list-paragraph {width: 100%;display: inline-block;}.ladi-list-paragraph a {text-decoration: underline;}.ladi-list-paragraph ul li {position: relative;counter-increment: linum;}.ladi-list-paragraph ul li:before {position: absolute;background-repeat: no-repeat;background-size: 100% 100%;left: 0;}.ladi-list-paragraph ul li:last-child {padding-bottom: 0 !important;}.ladi-line {position: relative;}.ladi-line .ladi-line-container {border-bottom: 0 !important;border-right: 0 !important;width: 100%;height: 100%;}a[data-action]{user-select: none;-webkit-user-select: none;-moz-user-select: none;-ms-user-select: none;cursor: pointer}a:visited{color: inherit}a:link{color: inherit}[data-opacity="0"]{opacity: 0}[data-hidden="true"]{display: none}[data-action="true"]{cursor: pointer}.ladi-hidden{display: none}.ladi-animation-hidden{visibility: hidden !important;opacity: 0 !important}.element-click-selected{cursor: pointer}.is-2nd-click{cursor: pointer}.ladi-button-shape.is-2nd-click,.ladi-accordion-shape.is-2nd-click{z-index: 1}.backdrop-popup{display: none;position: fixed;top: 0;left: 0;right: 0;bottom: 0;z-index: 90000060}.backdrop-dropbox{display: none;position: fixed;top: 0;left: 0;right: 0;bottom: 0;z-index: 90000040}.ladi-lazyload {background-image: none !important;}.ladi-list-paragraph ul li.ladi-lazyload:before {background-image: none !important;}#BODY_BACKGROUND {position: fixed;pointer-events: none;top: 0;left: 0;right: 0;margin: 0 auto;height: 100vh !important;}.ladi-element.ladi-auto-scroll {overflow-x: auto;overflow-y: hidden;width: 100% !important;left: 0 !important;-webkit-overflow-scrolling: touch;}[data-hint]:not([data-timeout-id-copied]):before, [data-hint]:not([data-timeout-id-copied]):after {display: none !important;}.ladi-section.ladi-auto-scroll {overflow-x: auto;overflow-y: hidden;-webkit-overflow-scrolling: touch;}.ladi-gallery .ladi-gallery-view > .ladi-gallery-view-item {transition: transform 300ms ease-in-out;}
    </style>
    <style type="text/css" id="style_animation">
      @media (min-width: 768px) {

        #GROUP2783,
        #GROUP2790,
        #GROUP2797,
        #GROUP2804,
        #GROUP3026,
        #BUTTON3040,
        #BUTTON3297,
        #BUTTON3325,
        #HEADLINE2539,
        #HEADLINE3491 {
          opacity: 0 !important;
          pointer-events: none !important;
        }
      }

      @media (max-width: 767px) {

        #GROUP2783,
        #GROUP2790,
        #GROUP2797,
        #GROUP2804,
        #GROUP3026,
        #BUTTON3040,
        #BUTTON3297,
        #IMAGE3334,
        #BUTTON3325,
        #HEADLINE3491 {
          opacity: 0 !important;
          pointer-events: none !important;
        }
      }
    </style>
    <style id="style_page" type="text/css">
      body {
        direction: ltr;
      }

      .ladi-wraper {
        margin: 0 auto;
        width: 420px;
      }

      body {
        font-family: "Open Sans", sans-serif
      }
    </style>
    <style id="style_element" type="text/css">
      #BODY_BACKGROUND,
      #SECTION_POPUP {
        height: 0px;
      }

      #SECTION3293 {
        height: 42.357px;
      }

      #SECTION3293>.ladi-section-background {
        background-color: rgb(191, 2, 2);
      }

      #HEADLINE3294 {
        width: 72px;
        top: 9.1474px;
        left: 35.2153px;
      }

      #HEADLINE3294>.ladi-headline,
      #HEADLINE3302>.ladi-headline,
      #HEADLINE3304>.ladi-headline {
        font-size: 11px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
      }

      #HEADLINE3294>.ladi-headline:hover,
      #SHAPE3296:hover>.ladi-shape,
      #BUTTON3297>.ladi-button:hover,
      #BUTTON_TEXT3297>.ladi-headline:hover,
      #SHAPE3299:hover>.ladi-shape,
      #HEADLINE3300>.ladi-headline:hover,
      #HEADLINE3302>.ladi-headline:hover,
      #HEADLINE3304>.ladi-headline:hover,
      #HEADLINE3308>.ladi-headline:hover,
      #HEADLINE3310>.ladi-headline:hover,
      #HEADLINE3311>.ladi-headline:hover,
      #HEADLINE3312>.ladi-headline:hover,
      #SHAPE3314:hover>.ladi-shape,
      #SHAPE3315:hover>.ladi-shape,
      #SHAPE3316:hover>.ladi-shape,
      #SHAPE3317:hover>.ladi-shape,
      #SHAPE3318:hover>.ladi-shape,
      #LIST_PARAGRAPH3320>.ladi-list-paragraph:hover,
      #LIST_PARAGRAPH3321>.ladi-list-paragraph:hover,
      #BOX3324>.ladi-box:hover,
      #BUTTON3325>.ladi-button:hover,
      #BUTTON_TEXT3325>.ladi-headline:hover,
      #HEADLINE3328>.ladi-headline:hover,
      #HEADLINE3329>.ladi-headline:hover,
      #HEADLINE3330>.ladi-headline:hover,
      #HEADLINE2539>.ladi-headline:hover,
      #IMAGE3334:hover>.ladi-image,
      #BOX3683>.ladi-box:hover,
      #HEADLINE3342>.ladi-headline:hover,
      #IMAGE3709:hover>.ladi-image,
      #BOX3707>.ladi-box:hover,
      #LIST_PARAGRAPH3684>.ladi-list-paragraph:hover,
      #IMAGE3758:hover>.ladi-image,
      #BOX3711>.ladi-box:hover,
      #BOX3684>.ladi-box:hover,
      #HEADLINE3687>.ladi-headline:hover,
      #SHAPE3687:hover>.ladi-shape,
      #HEADLINE3693>.ladi-headline:hover,
      #SHAPE3691:hover>.ladi-shape,
      #HEADLINE3710>.ladi-headline:hover,
      #BOX3698>.ladi-box:hover,
      #HEADLINE3711>.ladi-headline:hover,
      #BOX3709>.ladi-box:hover,
      #BOX3699>.ladi-box:hover,
      #HEADLINE3714>.ladi-headline:hover,
      #BOX3700>.ladi-box:hover,
      #HEADLINE3715>.ladi-headline:hover,
      #BOX3710>.ladi-box:hover,
      #HEADLINE3734>.ladi-headline:hover,
      #BOX3701>.ladi-box:hover,
      #HEADLINE3716>.ladi-headline:hover,
      #BOX3703>.ladi-box:hover,
      #HEADLINE3722>.ladi-headline:hover,
      #BOX3704>.ladi-box:hover,
      #HEADLINE3724>.ladi-headline:hover,
      #HEADLINE2777>.ladi-headline:hover,
      #BOX2784>.ladi-box:hover,
      #HEADLINE2785>.ladi-headline:hover,
      #HEADLINE2786>.ladi-headline:hover,
      #BOX2787>.ladi-box:hover,
      #BOX2788>.ladi-box:hover,
      #SHAPE2789:hover>.ladi-shape,
      #BOX2791>.ladi-box:hover,
      #HEADLINE2792>.ladi-headline:hover,
      #HEADLINE2793>.ladi-headline:hover,
      #BOX2794>.ladi-box:hover,
      #BOX2795>.ladi-box:hover,
      #SHAPE2796:hover>.ladi-shape,
      #BOX2798>.ladi-box:hover,
      #HEADLINE2799>.ladi-headline:hover,
      #HEADLINE2800>.ladi-headline:hover,
      #BOX2801>.ladi-box:hover,
      #BOX2802>.ladi-box:hover,
      #SHAPE2803:hover>.ladi-shape,
      #BOX2805>.ladi-box:hover,
      #HEADLINE2806>.ladi-headline:hover,
      #HEADLINE2807>.ladi-headline:hover,
      #BOX2808>.ladi-box:hover,
      #BOX2809>.ladi-box:hover,
      #SHAPE2810:hover>.ladi-shape,
      #HEADLINE2781>.ladi-headline:hover,
      #IMAGE1360:hover>.ladi-image,
      #HEADLINE1361>.ladi-headline:hover,
      #BUTTON1362>.ladi-button:hover,
      #BUTTON_TEXT1362>.ladi-headline:hover,
      #HEADLINE1364>.ladi-headline:hover,
      #IMAGE1367:hover>.ladi-image,
      #HEADLINE3587>.ladi-headline:hover,
      #HEADLINE3588>.ladi-headline:hover,
      #HEADLINE3590>.ladi-headline:hover,
      #SHAPE3591:hover>.ladi-shape,
      #BOX3592>.ladi-box:hover,
      #HEADLINE3593>.ladi-headline:hover,
      #SHAPE3596:hover>.ladi-shape,
      #SHAPE3597:hover>.ladi-shape,
      #SHAPE3598:hover>.ladi-shape,
      #SHAPE3599:hover>.ladi-shape,
      #SHAPE3600:hover>.ladi-shape,
      #PARAGRAPH3601>.ladi-paragraph:hover,
      #IMAGE3602:hover>.ladi-image,
      #PARAGRAPH3603>.ladi-paragraph:hover,
      #PARAGRAPH3604>.ladi-paragraph:hover,
      #PARAGRAPH3606>.ladi-paragraph:hover,
      #PARAGRAPH3607>.ladi-paragraph:hover,
      #IMAGE3608:hover>.ladi-image,
      #BOX3610>.ladi-box:hover,
      #PARAGRAPH3611>.ladi-paragraph:hover,
      #PARAGRAPH3612>.ladi-paragraph:hover,
      #PARAGRAPH3613>.ladi-paragraph:hover,
      #PARAGRAPH3614>.ladi-paragraph:hover,
      #IMAGE3637:hover>.ladi-image,
      #IMAGE3640:hover>.ladi-image,
      #PARAGRAPH3641>.ladi-paragraph:hover,
      #PARAGRAPH3642>.ladi-paragraph:hover,
      #PARAGRAPH3645>.ladi-paragraph:hover,
      #PARAGRAPH3646>.ladi-paragraph:hover,
      #PARAGRAPH3647>.ladi-paragraph:hover,
      #PARAGRAPH3648>.ladi-paragraph:hover,
      #SHAPE3650:hover>.ladi-shape,
      #SHAPE3651:hover>.ladi-shape,
      #SHAPE3652:hover>.ladi-shape,
      #SHAPE3653:hover>.ladi-shape,
      #SHAPE3654:hover>.ladi-shape,
      #PARAGRAPH3656>.ladi-paragraph:hover,
      #IMAGE3657:hover>.ladi-image,
      #PARAGRAPH3658>.ladi-paragraph:hover,
      #PARAGRAPH3659>.ladi-paragraph:hover,
      #BOX3682>.ladi-box:hover,
      #IMAGE3616:hover>.ladi-image,
      #PARAGRAPH3617>.ladi-paragraph:hover,
      #PARAGRAPH3618>.ladi-paragraph:hover,
      #SHAPE3622:hover>.ladi-shape,
      #SHAPE3623:hover>.ladi-shape,
      #SHAPE3624:hover>.ladi-shape,
      #SHAPE3625:hover>.ladi-shape,
      #SHAPE3626:hover>.ladi-shape,
      #PARAGRAPH3627>.ladi-paragraph:hover,
      #IMAGE3628:hover>.ladi-image,
      #PARAGRAPH3629>.ladi-paragraph:hover,
      #PARAGRAPH3630>.ladi-paragraph:hover,
      #BOX3632>.ladi-box:hover,
      #PARAGRAPH3633>.ladi-paragraph:hover,
      #PARAGRAPH3634>.ladi-paragraph:hover,
      #PARAGRAPH3635>.ladi-paragraph:hover,
      #PARAGRAPH3636>.ladi-paragraph:hover,
      #IMAGE3719:hover>.ladi-image,
      #IMAGE3128:hover>.ladi-image,
      #BOX3027>.ladi-box:hover,
      #HEADLINE3034>.ladi-headline:hover,
      #HEADLINE3035>.ladi-headline:hover,
      #HEADLINE3036>.ladi-headline:hover,
      #HEADLINE3037>.ladi-headline:hover,
      #HEADLINE3038>.ladi-headline:hover,
      #BUTTON3040>.ladi-button:hover,
      #BUTTON_TEXT3040>.ladi-headline:hover,
      #HEADLINE3046>.ladi-headline:hover,
      #PARAGRAPH3047>.ladi-paragraph:hover,
      #HEADLINE3048>.ladi-headline:hover,
      #HEADLINE3484>.ladi-headline:hover,
      #HEADLINE3487>.ladi-headline:hover,
      #HEADLINE3488>.ladi-headline:hover,
      #BOX3490>.ladi-box:hover,
      #HEADLINE3491>.ladi-headline:hover,
      #LIST_PARAGRAPH3530>.ladi-list-paragraph:hover,
      #BOX3531>.ladi-box:hover,
      #IMAGE3532:hover>.ladi-image,
      #HEADLINE220>.ladi-headline:hover,
      #SHAPE222:hover>.ladi-shape,
      #HEADLINE223>.ladi-headline:hover,
      #SHAPE225:hover>.ladi-shape,
      #HEADLINE226>.ladi-headline:hover,
      #SHAPE228:hover>.ladi-shape,
      #HEADLINE229>.ladi-headline:hover,
      #HEADLINE230>.ladi-headline:hover,
      #HEADLINE232>.ladi-headline:hover,
      #SHAPE233:hover>.ladi-shape,
      #HEADLINE235>.ladi-headline:hover,
      #SHAPE236:hover>.ladi-shape,
      #HEADLINE238>.ladi-headline:hover,
      #SHAPE239:hover>.ladi-shape,
      #HEADLINE241>.ladi-headline:hover,
      #SHAPE242:hover>.ladi-shape,
      #IMAGE244:hover>.ladi-image,
      #IMAGE245:hover>.ladi-image,
      #IMAGE246:hover>.ladi-image,
      #FRAME243>.ladi-frame:hover,
      #HEADLINE247>.ladi-headline:hover,
      #IMAGE329:hover>.ladi-image,
      #BOX331>.ladi-box:hover,
      #HEADLINE332>.ladi-headline:hover,
      #PARAGRAPH333>.ladi-paragraph:hover {
        opacity: 1;
      }

      #LINE3295,
      #LINE3301,
      #LINE3303 {
        height: 23px;
      }

      #LINE3295 {
        top: 6.6474px;
        left: 100.44px;
      }

      #LINE3295>.ladi-line>.ladi-line-container,
      #LINE3301>.ladi-line>.ladi-line-container,
      #LINE3303>.ladi-line>.ladi-line-container {
        border-top: 0px !important;
        border-right: 1px solid rgb(0, 0, 0);
        border-bottom: 1px solid rgb(0, 0, 0);
        border-left: 1px solid rgb(0, 0, 0);
      }

      #LINE3295>.ladi-line,
      #LINE3301>.ladi-line,
      #LINE3303>.ladi-line {
        height: 100%;
        padding: 0px 8px;
      }

      #SHAPE3296 {
        width: 29.86px;
        height: 34.3328px;
        top: 0.981px;
        left: 0px;
      }

      #SHAPE3296 svg:last-child,
      #SHAPE222 svg:last-child,
      #SHAPE225 svg:last-child,
      #SHAPE228 svg:last-child,
      #SHAPE233 svg:last-child,
      #SHAPE236 svg:last-child,
      #SHAPE239 svg:last-child,
      #SHAPE242 svg:last-child {
        fill: rgb(0, 0, 0);
      }

      #BUTTON3297 {
        width: 97.6827px;
        height: 28.0788px;
        top: 6.6474px;
        left: 321.265px;
      }

      #BUTTON3297>.ladi-button>.ladi-button-background,
      #BOX2788>.ladi-box,
      #BOX2795>.ladi-box,
      #BOX2802>.ladi-box,
      #BOX2809>.ladi-box {
        background-color: rgb(255, 188, 1);
      }

      #BUTTON3297>.ladi-button {
        border-radius: 30px;
      }

      #BUTTON3297.ladi-animation>.ladi-button,
      #BUTTON3040.ladi-animation>.ladi-button,
      #HEADLINE3491.ladi-animation>.ladi-headline {
        animation-name: pulse;
        animation-delay: 1s;
        animation-duration: 1s;
        animation-iteration-count: infinite;
      }

      #BUTTON_TEXT3297 {
        width: 98px;
        top: 10.5341px;
        left: 0px;
      }

      #BUTTON_TEXT3297>.ladi-headline {
        font-size: 12px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #SHAPE3299 {
        width: 18.8119px;
        height: 21.6297px;
        top: 9.33255px;
        left: 322.225px;
      }

      #SHAPE3299 svg:last-child,
      #SHAPE2789 svg:last-child,
      #SHAPE2796 svg:last-child,
      #SHAPE2803 svg:last-child,
      #SHAPE2810 svg:last-child {
        fill: rgb(255, 255, 255);
      }

      #HEADLINE3300 {
        width: 68px;
        top: 9.1474px;
        left: 113.504px;
      }

      #HEADLINE3300>.ladi-headline {
        font-size: 11px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: left;
      }

      #LINE3301 {
        top: 6.6474px;
        left: 173.735px;
      }

      #HEADLINE3302 {
        width: 63px;
        top: 9.1474px;
        left: 192.058px;
      }

      #LINE3303 {
        top: 6.6474px;
        left: 248.047px;
      }

      #HEADLINE3304 {
        width: 51px;
        top: 9.1474px;
        left: 265.047px;
      }

      #SECTION2590 {
        height: 710.467px;
      }

      #SECTION2590>.ladi-section-background {
        background-color: rgb(5, 5, 5);
      }

      #GALLERY3549 {
        width: 420px;
        height: 491.36px;
        top: 53.39px;
        left: 0px;
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view .ladi-gallery-view-arrow,
      #GALLERY3549 .ladi-gallery .ladi-gallery-control .ladi-gallery-control-arrow {
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23fff%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M7.00015%200.585938L18.4144%2012.0002L7.00015%2023.4144L5.58594%2022.0002L15.5859%2012.0002L5.58594%202.00015L7.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
      }

      #GALLERY3549>.ladi-gallery>.ladi-gallery-view {
        height: calc(100% - 85px);
      }

      #GALLERY3549>.ladi-gallery>.ladi-gallery-control {
        height: 80px;
      }

      #GALLERY3549>.ladi-gallery>.ladi-gallery-control>.ladi-gallery-control-box>.ladi-gallery-control-item {
        width: 80px;
        height: 80px;
        margin-right: 5px;
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="0"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/mn1-20230612044456-dq4xs.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="0"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/mn1-20230612044456-dq4xs.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="1"],
      #IMAGE3761>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/o1cn01meddp22frmofcyjs3_2210971298933-0-cib-20230523081820-uuw0r.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="1"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/o1cn01meddp22frmofcyjs3_2210971298933-0-cib-20230523081820-uuw0r.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="2"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/t2rz-20221229023257-bak8--20230523081759-sgx5j.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="2"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/t2rz-20221229023257-bak8--20230523081759-sgx5j.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="3"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/ct17-20221229070645-6j5zz-20230523081759-fy279.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="3"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/ct17-20221229070645-6j5zz-20230523081759-fy279.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="4"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/343428781_903845187566437_2878504481826019921_n-20230523081758-kilzb.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="4"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/343428781_903845187566437_2878504481826019921_n-20230523081758-kilzb.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="5"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/mmm-20230612044456-zzijc.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="5"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/mmm-20230612044456-zzijc.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="6"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/t4rz-20221229023257-njnhu-20230523081759-jzl_9.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="6"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/t4rz-20221229023257-njnhu-20230523081759-jzl_9.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="7"],
      #IMAGE3727>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/siucap5-20230331033452-0meyc-20230523081759-sufnk.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="7"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/siucap5-20230331033452-0meyc-20230523081759-sufnk.jpg");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="8"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/o1cn013ygkak1ucvavgesc0_2214127126001-0-cib-20230523081819-uob7x.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="8"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/o1cn013ygkak1ucvavgesc0_2214127126001-0-cib-20230523081819-uob7x.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-view-item[data-index="9"] {
        background-image: url("https://w.ladicdn.com/s750x800/5dbe4694ed94dc587f3c244b/siucap1-removebg-20230331021854-qhjsw-20230523081924-bhfwj.png");
      }

      #GALLERY3549 .ladi-gallery .ladi-gallery-control-item[data-index="9"] {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/siucap1-removebg-20230331021854-qhjsw-20230523081924-bhfwj.png");
      }

      #HEADLINE3308,
      #HEADLINE2777 {
        width: 400px;
      }

      #HEADLINE3308 {
        top: 129.717px;
        left: 0px;
      }

      #HEADLINE3308>.ladi-headline {
        font-size: 12px;
        font-style: italic;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #HEADLINE3310 {
        width: 52px;
        top: 0.5px;
        left: 0px;
      }

      #HEADLINE3310>.ladi-headline {
        font-size: 14px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(191, 2, 2);
        text-align: justify;
      }

      #HEADLINE3311 {
        width: 66px;
        top: 0.5px;
        left: 41px;
      }

      #HEADLINE3311>.ladi-headline {
        font-size: 14px;
        line-height: 1.6;
        color: rgb(199, 31, 22);
        text-align: justify;
      }

      #HEADLINE3312 {
        width: 34px;
      }

      #HEADLINE3312,
      #FRAME243 {
        top: 0px;
        left: 95px;
      }

      #HEADLINE3312>.ladi-headline {
        font-size: 14px;
        line-height: 1.6;
        color: rgb(199, 31, 22);
        text-decoration-line: underline;
        text-align: justify;
      }

      #SHAPE3314,
      #SHAPE3315,
      #SHAPE3316,
      #SHAPE3317 {
        width: 16px;
        height: 16px;
      }

      #SHAPE3314,
      #LIST_PARAGRAPH3320,
      #BOX3324,
      #HEADLINE3329,
      #GROUP3323,
      #IMAGE3334>.ladi-image>.ladi-image-background,
      #BOX3683,
      #IMAGE3746>.ladi-image>.ladi-image-background,
      #BOX3707,
      #IMAGE3758>.ladi-image>.ladi-image-background,
      #BOX3684,
      #SHAPE3687,
      #SHAPE3691,
      #BOX3698,
      #IMAGE3725>.ladi-image>.ladi-image-background,
      #IMAGE3748>.ladi-image>.ladi-image-background,
      #SHAPE3698,
      #IMAGE3761>.ladi-image>.ladi-image-background,
      #SHAPE3699,
      #BOX3699,
      #IMAGE3727>.ladi-image>.ladi-image-background,
      #IMAGE3757>.ladi-image>.ladi-image-background,
      #BOX3700,
      #IMAGE3728>.ladi-image>.ladi-image-background,
      #BOX3710,
      #IMAGE3762>.ladi-image>.ladi-image-background,
      #IMAGE3763>.ladi-image>.ladi-image-background,
      #BOX3701,
      #IMAGE3729>.ladi-image>.ladi-image-background,
      #IMAGE3731>.ladi-image>.ladi-image-background,
      #BOX3703,
      #IMAGE3747>.ladi-image>.ladi-image-background,
      #IMAGE3738,
      #IMAGE3738>.ladi-image>.ladi-image-background,
      #IMAGE3741>.ladi-image>.ladi-image-background,
      #GROUP3719,
      #IMAGE3749>.ladi-image>.ladi-image-background,
      #BOX3704,
      #IMAGE3745>.ladi-image>.ladi-image-background,
      #BOX2784,
      #BOX2791,
      #BOX2798,
      #BOX2805,
      #HEADLINE2781,
      #IMAGE1367>.ladi-image>.ladi-image-background,
      #SHAPE3596,
      #GROUP3595,
      #IMAGE3602>.ladi-image>.ladi-image-background,
      #GROUP3639,
      #PARAGRAPH3646,
      #SHAPE3650,
      #IMAGE3657>.ladi-image>.ladi-image-background,
      #GROUP3638,
      #GROUP3615,
      #SHAPE3622,
      #GROUP3621,
      #IMAGE3628>.ladi-image>.ladi-image-background,
      #GROUP3620,
      #IMAGE3719>.ladi-image>.ladi-image-background,
      #IMAGE3128>.ladi-image>.ladi-image-background,
      #BOX3027,
      #GROUP3026,
      #BOX3490,
      #GROUP3489,
      #GROUP3485,
      #BOX3531,
      #IMAGE3532>.ladi-image>.ladi-image-background,
      #SHAPE225,
      #SHAPE228,
      #SHAPE233,
      #SHAPE236,
      #SHAPE239,
      #SHAPE242,
      #IMAGE244>.ladi-image>.ladi-image-background,
      #IMAGE245>.ladi-image>.ladi-image-background,
      #IMAGE246>.ladi-image>.ladi-image-background,
      #POPUP328,
      #IMAGE329>.ladi-image>.ladi-image-background,
      #BOX331 {
        top: 0px;
        left: 0px;
      }

      #SHAPE3314 svg:last-child,
      #SHAPE3315 svg:last-child,
      #SHAPE3316 svg:last-child,
      #SHAPE3317 svg:last-child,
      #SHAPE3318 svg:last-child,
      #SHAPE3687 svg:last-child,
      #SHAPE3691 svg:last-child,
      #SHAPE3596 svg:last-child,
      #SHAPE3597 svg:last-child,
      #SHAPE3598 svg:last-child,
      #SHAPE3599 svg:last-child,
      #SHAPE3600 svg:last-child,
      #SHAPE3650 svg:last-child,
      #SHAPE3651 svg:last-child,
      #SHAPE3652 svg:last-child,
      #SHAPE3653 svg:last-child,
      #SHAPE3654 svg:last-child,
      #SHAPE3622 svg:last-child,
      #SHAPE3623 svg:last-child,
      #SHAPE3624 svg:last-child,
      #SHAPE3625 svg:last-child,
      #SHAPE3626 svg:last-child {
        fill: rgb(255, 188, 1);
      }

      #SHAPE3315 {
        top: 0px;
        left: 17.6px;
      }

      #SHAPE3316 {
        top: 0px;
        left: 35.2px;
      }

      #SHAPE3317 {
        top: 0px;
        left: 52.8px;
      }

      #SHAPE3318 {
        width: 17.6px;
        height: 16px;
        top: 0px;
        left: 68.8px;
      }

      #GROUP3313 {
        width: 86.4px;
        height: 16px;
        top: 2.5px;
        left: 120px;
      }

      #GROUP3309 {
        width: 206.4px;
        height: 22.5px;
        top: 107.217px;
        left: 96.8px;
      }

      #LIST_PARAGRAPH3320,
      #LIST_PARAGRAPH3321 {
        width: 102px;
      }

      #LIST_PARAGRAPH3320>.ladi-list-paragraph,
      #LIST_PARAGRAPH3321>.ladi-list-paragraph {
        font-size: 14px;
        line-height: 1.6;
        color: rgb(229, 85, 70);
      }

      #LIST_PARAGRAPH3320 ul li,
      #LIST_PARAGRAPH3321 ul li {
        padding-left: 31px;
      }

      #LIST_PARAGRAPH3320 ul li:before,
      #LIST_PARAGRAPH3321 ul li:before {
        width: 26px;
        height: 26px;
        top: -3px;
      }

      #LIST_PARAGRAPH3320 ul li:before {
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20xmlns%3Asketch%3D%22http%3A%2F%2Fwww.bohemiancoding.com%2Fsketch%2Fns%22%20viewBox%3D%220%200%20100%20100%22%20version%3D%221.1%22%20x%3D%220px%22%20y%3D%220px%22%20%20width%3D%22100%25%22%20height%3D%22100%25%22%20class%3D%22%22%20fill%3D%22rgba(229%2C%2085%2C%2070%2C%201.0)%22%3E%3Ctitle%3ESale-38%3C%2Ftitle%3E%3Cdesc%3ECreated%20with%20Sketch.%3C%2Fdesc%3E%3Cg%20stroke%3D%22none%22%20stroke-width%3D%221%22%20fill-rule%3D%22evenodd%22%20sketch%3Atype%3D%22MSPage%22%3E%3Cg%20sketch%3Atype%3D%22MSLayerGroup%22%3E%3Cpath%20d%3D%22M50%2C86.270009%20L37.1916646%2C94.0839298%20L30.6411316%2C80.5857324%20L15.6415497%2C80.2345119%20L17.4285672%2C65.3376202%20L5%2C56.9327684%20L14.5572025%2C45.3668402%20L8.64563237%2C31.5768383%20L22.9386756%2C27.0139741%20L25.4209843%2C12.2170502%20L39.911928%2C16.1059271%20L50%2C5%20L60.088072%2C16.1059271%20L74.5790157%2C12.2170502%20L77.0613244%2C27.0139741%20L91.3543676%2C31.5768383%20L85.4427975%2C45.3668402%20L95%2C56.9327684%20L82.5714328%2C65.3376202%20L84.3584503%2C80.2345119%20L69.3588684%2C80.5857324%20L62.8083354%2C94.0839298%20L50%2C86.270009%20Z%20M59.8248803%2C59.2266863%20C59.8248803%2C58.6300845%2059.8512006%2C57.9633042%2059.9038419%2C57.2263255%20C59.9564833%2C56.4893468%2060.1056315%2C55.8050197%2060.3512911%2C55.1733237%20C60.5969506%2C54.5416276%2060.956661%2C54.0152222%2061.4304331%2C53.5940915%20C61.9042051%2C53.1729608%2062.5622119%2C52.9623986%2063.4044733%2C52.9623986%20C64.1765463%2C52.9623986%2064.7819126%2C53.199281%2065.2205904%2C53.6730531%20C65.6592682%2C54.1468251%2065.9838849%2C54.7083243%2066.1944502%2C55.3575674%20C66.4050156%2C56.0068106%2066.5366169%2C56.6911377%2066.5892583%2C57.4105693%20C66.6418996%2C58.1300009%2066.6682199%2C58.752914%2066.6682199%2C59.2793274%20C66.6682199%2C59.840835%2066.6418996%2C60.481295%2066.5892583%2C61.2007266%20C66.5366169%2C61.9201582%2066.3962421%2C62.6044853%2066.1681297%2C63.2537284%20C65.9400172%2C63.9029716%2065.5890802%2C64.4556973%2065.1153082%2C64.9119222%20C64.6415362%2C65.3681472%2064.0010762%2C65.5962562%2063.193909%2C65.5962562%20C62.3516476%2C65.5962562%2061.7024142%2C65.3681472%2061.2461893%2C64.9119222%20C60.7899644%2C64.4556973%2060.4565743%2C63.9029716%2060.2460089%2C63.2537284%20C60.0354436%2C62.6044853%2059.9126156%2C61.9113847%2059.8775214%2C61.174406%20C59.8424272%2C60.4374273%2059.8248803%2C59.7881939%2059.8248803%2C59.2266863%20Z%20M54.4554909%2C59.1740452%20C54.4554909%2C60.6129084%2054.6046391%2C61.9640158%2054.90294%2C63.2274079%20C55.2012409%2C64.4908%2055.6925527%2C65.587478%2056.37689%2C66.517475%20C57.0612274%2C67.4474719%2057.9561167%2C68.1756662%2059.0615848%2C68.7020795%20C60.1670528%2C69.2284929%2061.5269336%2C69.4916956%2063.141268%2C69.4916956%20C64.6854138%2C69.4916956%2066.0189744%2C69.2372663%2067.1419895%2C68.7284001%20C68.2650047%2C68.2195338%2069.1862143%2C67.5088864%2069.9056459%2C66.5964366%20C70.6250775%2C65.6839868%2071.1602564%2C64.613629%2071.5111986%2C63.3853311%20C71.8621409%2C62.1570333%2072.0376094%2C60.8234727%2072.0376094%2C59.3846095%20C72.0376094%2C57.9457463%2071.8972346%2C56.594639%2071.6164808%2C55.3312469%20C71.335727%2C54.0678548%2070.8531886%2C52.9711768%2070.1688513%2C52.0411798%20C69.4845139%2C51.1111829%2068.598398%2C50.3742152%2067.5104771%2C49.8302547%20C66.4225561%2C49.2862942%2065.0539019%2C49.0143181%2063.4044733%2C49.0143181%20C61.8603275%2C49.0143181%2060.5267669%2C49.2775208%2059.4037517%2C49.8039342%20C58.2807366%2C50.3303476%2057.3507536%2C51.0497684%2056.6137749%2C51.9622182%20C55.8767961%2C52.874668%2055.3328438%2C53.9450258%2054.9819016%2C55.1733237%20C54.6309593%2C56.4016215%2054.4554909%2C57.735182%2054.4554909%2C59.1740452%20L54.4554909%2C59.1740452%20Z%20M58.5088535%2C30.8005067%20L36.7154491%2C69.9128242%20L41.2952225%2C69.9128242%20L62.9833447%2C30.8005067%20L58.5088535%2C30.8005067%20Z%20M32.6620865%2C41.74985%20C32.6620865%2C41.1532481%2032.6884067%2C40.468921%2032.7410481%2C39.6968481%20C32.7936894%2C38.9247752%2032.9428376%2C38.2053544%2033.1884972%2C37.5385641%20C33.4341568%2C36.8717738%2033.7938672%2C36.3102747%2034.2676392%2C35.8540497%20C34.7414112%2C35.3978248%2035.3994181%2C35.1697158%2036.2416795%2C35.1697158%20C37.0137524%2C35.1697158%2037.6191187%2C35.4153717%2038.0577965%2C35.9066908%20C38.4964743%2C36.39801%2038.821091%2C36.9946028%2039.0316563%2C37.6964873%20C39.2422217%2C38.3983718%2039.3738231%2C39.126566%2039.4264644%2C39.8810919%20C39.4791057%2C40.6356177%2039.505426%2C41.2760777%2039.505426%2C41.802491%20C39.505426%2C42.3639986%2039.4791057%2C42.9869118%2039.4264644%2C43.6712491%20C39.3738231%2C44.3555865%2039.2334483%2C45.0048199%2039.0053358%2C45.6189689%20C38.7772234%2C46.2331178%2038.4262864%2C46.7507498%2037.9525143%2C47.1718805%20C37.4787423%2C47.5930112%2036.8382823%2C47.8035734%2036.0311152%2C47.8035734%20C35.1888538%2C47.8035734%2034.5396204%2C47.5930112%2034.0833955%2C47.1718805%20C33.6271705%2C46.7507498%2033.2937804%2C46.2331178%2033.0832151%2C45.6189689%20C32.8726497%2C45.0048199%2032.7498218%2C44.3468131%2032.7147275%2C43.6449286%20C32.6796333%2C42.9430441%2032.6620865%2C42.3113575%2032.6620865%2C41.74985%20L32.6620865%2C41.74985%20Z%20M27.292697%2C41.6445678%20C27.292697%2C43.083431%2027.4418452%2C44.425765%2027.7401461%2C45.6716099%20C28.038447%2C46.9174549%2028.5297588%2C47.9878127%2029.2140962%2C48.8827154%20C29.8984335%2C49.7776181%2030.7933228%2C50.4794921%2031.8987909%2C50.9883583%20C33.004259%2C51.4972246%2034.3641398%2C51.7516539%2035.9784741%2C51.7516539%20C37.52262%2C51.7516539%2038.8561805%2C51.505998%2039.9791957%2C51.0146789%20C41.1022109%2C50.5233597%2042.0234204%2C49.8390326%2042.742852%2C48.961677%20C43.4622836%2C48.0843214%2043.9974625%2C47.0402839%2044.3484048%2C45.8295331%20C44.699347%2C44.6187824%2044.8748155%2C43.2939953%2044.8748155%2C41.8551321%20C44.8748155%2C40.4162689%2044.7344407%2C39.0476147%2044.4536869%2C37.7491284%20C44.1729331%2C36.4506421%2043.6903948%2C35.3188703%2043.0060574%2C34.3537791%20C42.32172%2C33.388688%2041.4356041%2C32.6254%2040.3476832%2C32.0638925%20C39.2597622%2C31.5023849%2037.891108%2C31.2216353%2036.2416795%2C31.2216353%20C34.6975336%2C31.2216353%2033.3639731%2C31.4936114%2032.2409579%2C32.0375719%20C31.1179427%2C32.5815324%2030.1879597%2C33.3272735%2029.450981%2C34.2748175%20C28.7140023%2C35.2223616%2028.17005%2C36.3278131%2027.8191077%2C37.5912052%20C27.4681655%2C38.8545972%2027.292697%2C40.2057046%2027.292697%2C41.6445678%20L27.292697%2C41.6445678%20Z%22%20sketch%3Atype%3D%22MSShapeGroup%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E");
      }

      #LIST_PARAGRAPH3320 ul li:before,
      #LIST_PARAGRAPH3321 ul li:before,
      #LIST_PARAGRAPH3684 ul li:before,
      #LIST_PARAGRAPH3530 ul li:before {
        content: "";
      }

      #LIST_PARAGRAPH3321 {
        top: 0px;
        left: 97px;
      }

      #LIST_PARAGRAPH3321 ul li:before {
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20xmlns%3Axlink%3D%22http%3A%2F%2Fwww.w3.org%2F1999%2Fxlink%22%20version%3D%221.1%22%20x%3D%220px%22%20y%3D%220px%22%20viewBox%3D%220%200%2032%2032%22%20enable-background%3D%22new%200%200%2032%2032%22%20xml%3Aspace%3D%22preserve%22%20%20width%3D%22100%25%22%20height%3D%22100%25%22%20class%3D%22%22%20fill%3D%22rgba(229%2C%2085%2C%2070%2C%201.0)%22%3E%3Cg%20display%3D%22none%22%3E%3Crect%20x%3D%22-337%22%20y%3D%22-109%22%20display%3D%22inline%22%20width%3D%22461.5%22%20height%3D%22244%22%3E%3C%2Frect%3E%3C%2Fg%3E%3Cg%3E%3Cg%3E%3Cpath%20d%3D%22M31.99%2C22.997V16.47l-4.252-6.807h-6.704v13.333h2.334c0-0.008-0.003-0.016-0.003-0.025c0%2C1.749%2C1.418%2C3.167%2C3.167%2C3.167%20%20%20%20s3.167-1.418%2C3.167-3.167c0%2C0.008-0.002%2C0.016-0.003%2C0.025H31.99z%20M23.744%2C14.72v-2.376h2.607l1.293%2C2.376H23.744z%22%3E%3C%2Fpath%3E%3Cpath%20d%3D%22M17.846%2C22.997h1.687V5.872H0.01v17.125h1.701c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142s3.151-1.405%2C3.164-3.142h3.478%20%20%20%20c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142S17.832%2C24.734%2C17.846%2C22.997z%20M8.32%2C18.971l-4.123-3.663l0.996-1.121l2.928%2C2.601%20%20%20%20l6.045-7.742l1.182%2C0.924L8.32%2C18.971z%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3C%2Fg%3E%3Cg%20display%3D%22none%22%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M31.99%2C22.992v-6.527l-4.252-6.807h-6.704v13.333h2.334c0-0.008-0.003-0.016-0.003-0.025%20%20%20c0%2C1.749%2C1.418%2C3.167%2C3.167%2C3.167s3.167-1.418%2C3.167-3.167c0%2C0.008-0.002%2C0.016-0.003%2C0.025H31.99z%20M26.531%2C24.636%20%20%20c-0.922%2C0-1.67-0.747-1.67-1.67s0.748-1.67%2C1.67-1.67s1.67%2C0.747%2C1.67%2C1.67S27.453%2C24.636%2C26.531%2C24.636z%20M29.317%2C21.492%20%20%20c-0.532-1.002-1.573-1.692-2.786-1.692s-2.254%2C0.69-2.786%2C1.692l0%2C0h-1.212V11.158h4.373l3.584%2C5.737v4.597L29.317%2C21.492%20%20%20L29.317%2C21.492z%20M29.599%2C22.216c-0.006-0.023-0.015-0.044-0.021-0.067C29.584%2C22.171%2C29.593%2C22.193%2C29.599%2C22.216z%20M23.464%2C22.216%20%20%20c0.006-0.023%2C0.014-0.044%2C0.021-0.067C23.479%2C22.172%2C23.47%2C22.193%2C23.464%2C22.216z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M23.699%2C14.587v-2.297h2.52l1.25%2C2.297H23.699z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M0.01%2C5.867v17.125h1.701c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142s3.151-1.405%2C3.164-3.142h3.493%20%20%20c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142s3.151-1.405%2C3.164-3.142h1.662V5.867H0.01z%20M4.875%2C24.633c-0.911%2C0-1.651-0.735-1.664-1.642%20%20%20h3.328C6.526%2C23.899%2C5.786%2C24.633%2C4.875%2C24.633z%20M8.32%2C19.34l-4.123-3.663l0.996-1.121l2.928%2C2.601l6.045-7.742l1.182%2C0.924%20%20%20L8.32%2C19.34z%20M14.697%2C24.633c-0.911%2C0-1.65-0.735-1.664-1.642h3.328C16.347%2C23.899%2C15.607%2C24.633%2C14.697%2C24.633z%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3Cg%20display%3D%22none%22%3E%3Cpath%20display%3D%22inline%22%20stroke%3D%22%231A1718%22%20stroke-width%3D%220.5%22%20stroke-miterlimit%3D%2210%22%20d%3D%22M31.99%2C22.992v-6.527%20%20%20l-4.252-6.807h-6.704v13.333h2.334c0-0.008-0.003-0.016-0.003-0.025c0%2C1.749%2C1.418%2C3.167%2C3.167%2C3.167s3.167-1.418%2C3.167-3.167%20%20%20c0%2C0.008-0.002%2C0.016-0.003%2C0.025H31.99z%20M26.531%2C24.636c-0.922%2C0-1.67-0.747-1.67-1.67s0.748-1.67%2C1.67-1.67s1.67%2C0.747%2C1.67%2C1.67%20%20%20S27.453%2C24.636%2C26.531%2C24.636z%22%3E%3C%2Fpath%3E%3Cg%20display%3D%22inline%22%3E%3Cpath%20d%3D%22M25.922%2C12.789l0.706%2C1.297h-2.429v-1.297H25.922%20M26.219%2C12.289h-2.52v2.297h3.77L26.219%2C12.289%20%20%20%20L26.219%2C12.289z%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3Cg%20display%3D%22inline%22%3E%3Cpath%20d%3D%22M19.023%2C6.367v16.125h-1.162h-0.496l-0.004%2C0.496c-0.012%2C1.459-1.207%2C2.646-2.664%2C2.646%20%20%20%20c-1.458%2C0-2.653-1.187-2.664-2.646l-0.004-0.496h-0.496H8.039H7.543l-0.004%2C0.496c-0.012%2C1.459-1.207%2C2.646-2.664%2C2.646%20%20%20%20s-2.653-1.187-2.664-2.646l-0.004-0.496H1.711H0.51V6.367H19.023%20M8.054%2C16.429l-2.529-2.247L5.151%2C13.85l-0.332%2C0.374%20%20%20%20l-0.996%2C1.121l-0.332%2C0.374l0.374%2C0.332l4.123%2C3.663l0.398%2C0.354l0.328-0.42l7.027-9.002l0.307-0.394l-0.394-0.308L14.473%2C9.02%20%20%20%20l-0.394-0.308l-0.308%2C0.394L8.054%2C16.429%20M14.697%2C25.133c1.176%2C0%2C2.146-0.957%2C2.164-2.134l0.008-0.508h-0.508h-3.328h-0.508%20%20%20%20l0.008%2C0.508C12.551%2C24.176%2C13.521%2C25.133%2C14.697%2C25.133%20M4.875%2C25.133c1.176%2C0%2C2.146-0.957%2C2.164-2.134l0.008-0.508H6.539H3.211%20%20%20%20H2.703l0.008%2C0.508C2.729%2C24.176%2C3.7%2C25.133%2C4.875%2C25.133%20M19.523%2C5.867H0.01v17.125h1.701c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142%20%20%20%20s3.151-1.405%2C3.164-3.142h3.493c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142s3.151-1.405%2C3.164-3.142h1.662V5.867L19.523%2C5.867z%20%20%20%20%20M8.12%2C17.156l6.045-7.742l1.182%2C0.924L8.32%2C19.34l-4.123-3.663l0.996-1.121L8.12%2C17.156L8.12%2C17.156z%20M14.697%2C24.633%20%20%20%20c-0.911%2C0-1.65-0.735-1.664-1.642h3.328C16.347%2C23.899%2C15.607%2C24.633%2C14.697%2C24.633L14.697%2C24.633z%20M4.875%2C24.633%20%20%20%20c-0.911%2C0-1.651-0.735-1.664-1.642h3.328C6.526%2C23.899%2C5.786%2C24.633%2C4.875%2C24.633L4.875%2C24.633z%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3C%2Fg%3E%3Cg%20display%3D%22none%22%3E%3Ccircle%20display%3D%22inline%22%20cx%3D%2226.531%22%20cy%3D%2222.967%22%20r%3D%221.67%22%3E%3C%2Fcircle%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M26.531%2C19.8c1.213%2C0%2C2.254%2C0.69%2C2.786%2C1.692l0%2C0h1.172v-4.597l-3.584-5.737h-4.373%20%20%20v10.333h1.212l0%2C0C24.277%2C20.49%2C25.318%2C19.8%2C26.531%2C19.8z%20M23.699%2C14.587v-2.297h2.52l1.25%2C2.297H23.699z%22%3E%3C%2Fpath%3E%3Cpolygon%20display%3D%22inline%22%20points%3D%2215.347%2C10.338%2014.165%2C9.414%208.12%2C17.156%205.193%2C14.556%204.197%2C15.677%208.32%2C19.34%20%20%20%20%20%22%3E%3C%2Fpolygon%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M6.539%2C22.992H3.211c0.014%2C0.907%2C0.754%2C1.642%2C1.664%2C1.642S6.526%2C23.899%2C6.539%2C22.992z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M13.033%2C22.992c0.014%2C0.907%2C0.754%2C1.642%2C1.664%2C1.642s1.651-0.735%2C1.664-1.642H13.033z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M31.99%2C22.992v-6.527l-4.252-6.807h-6.704v13.333h2.334c0-0.008-0.003-0.016-0.003-0.025%20%20%20c0%2C1.749%2C1.418%2C3.167%2C3.167%2C3.167s3.167-1.418%2C3.167-3.167c0%2C0.008-0.002%2C0.016-0.003%2C0.025H31.99z%20M24.862%2C22.967%20%20%20c0-0.922%2C0.748-1.67%2C1.67-1.67s1.67%2C0.747%2C1.67%2C1.67s-0.747%2C1.67-1.67%2C1.67S24.862%2C23.889%2C24.862%2C22.967z%20M22.533%2C21.492V11.158%20%20%20h4.373l3.584%2C5.737v4.597h-1.172l0%2C0c-0.532-1.002-1.573-1.692-2.786-1.692s-2.254%2C0.69-2.786%2C1.692l0%2C0H22.533z%22%3E%3C%2Fpath%3E%3Cpolygon%20display%3D%22inline%22%20points%3D%2226.219%2C12.289%2023.699%2C12.289%2023.699%2C14.587%2027.469%2C14.587%20%20%22%3E%3C%2Fpolygon%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M17.861%2C22.992h1.662V5.867H0.01v17.125h1.701c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142%20%20%20s3.151-1.405%2C3.164-3.142h3.493c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142S17.847%2C24.729%2C17.861%2C22.992z%20M14.697%2C24.633%20%20%20c-0.911%2C0-1.65-0.735-1.664-1.642h3.328C16.347%2C23.899%2C15.607%2C24.633%2C14.697%2C24.633z%20M5.193%2C14.556l2.928%2C2.601l6.045-7.742%20%20%20l1.182%2C0.924L8.32%2C19.34l-4.123-3.663L5.193%2C14.556z%20M3.211%2C22.992h3.328c-0.014%2C0.907-0.754%2C1.642-1.664%2C1.642%20%20%20S3.225%2C23.899%2C3.211%2C22.992z%22%3E%3C%2Fpath%3E%3C%2Fg%3E%3Cg%20display%3D%22none%22%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M16%2C32c8.837%2C0%2C16-7.163%2C16-16S24.837%2C0%2C16%2C0S0%2C7.163%2C0%2C16S7.163%2C32%2C16%2C32z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M14.697%2C24.633c0.911%2C0%2C1.651-0.735%2C1.664-1.642h-3.328%20%20%20C13.047%2C23.899%2C13.786%2C24.633%2C14.697%2C24.633z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M4.875%2C24.633c0.911%2C0%2C1.651-0.735%2C1.664-1.642H3.211%20%20%20C3.225%2C23.899%2C3.965%2C24.633%2C4.875%2C24.633z%22%3E%3C%2Fpath%3E%3Ccircle%20display%3D%22inline%22%20cx%3D%2226.531%22%20cy%3D%2222.967%22%20r%3D%221.67%22%3E%3C%2Fcircle%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M29.318%2C21.492L29.318%2C21.492l1.172%2C0v-4.597l-3.584-5.737h-4.373v10.333h1.212l0%2C0%20%20%20c0.532-1.002%2C1.573-1.692%2C2.786-1.692S28.786%2C20.49%2C29.318%2C21.492z%20M23.699%2C14.587v-2.297h2.52l1.25%2C2.297H23.699z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M17.861%2C22.992h-1.5c-0.014%2C0.907-0.754%2C1.642-1.664%2C1.642s-1.65-0.735-1.664-1.642h-1.5%20%20%20c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142S17.847%2C24.729%2C17.861%2C22.992z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M6.539%2C22.992c-0.014%2C0.907-0.754%2C1.642-1.664%2C1.642s-1.651-0.735-1.664-1.642h-1.5%20%20%20c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142s3.151-1.405%2C3.164-3.142H6.539z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M31.99%2C22.992v-6.527l-4.252-6.807h-6.704v13.333h2.334c0-0.008-0.003-0.016-0.003-0.025%20%20%20c0%2C1.749%2C1.418%2C3.167%2C3.167%2C3.167s3.167-1.418%2C3.167-3.167c0%2C0.008-0.002%2C0.016-0.003%2C0.025H31.99z%20M26.531%2C24.636%20%20%20c-0.922%2C0-1.67-0.747-1.67-1.67s0.748-1.67%2C1.67-1.67s1.67%2C0.747%2C1.67%2C1.67S27.453%2C24.636%2C26.531%2C24.636z%20M26.531%2C19.8%20%20%20c-1.213%2C0-2.254%2C0.69-2.786%2C1.692l0%2C0h-1.212V11.158h4.373l3.584%2C5.737v4.597h-1.172l0%2C0C28.786%2C20.49%2C27.744%2C19.8%2C26.531%2C19.8z%22%3E%3C%2Fpath%3E%3Cpolygon%20display%3D%22inline%22%20points%3D%2223.699%2C12.289%2023.699%2C14.587%2027.469%2C14.587%2026.219%2C12.289%20%20%22%3E%3C%2Fpolygon%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M18.523%2C6.867v15.125h-0.662h-1.5h-3.328h-1.5H8.039h-1.5H3.211h-1.5H1.01V6.867H18.523%20%20%20%20M7.988%2C15.701l-2.131-1.893l-0.748-0.664l-0.664%2C0.747l-0.996%2C1.121L2.785%2C15.76l0.748%2C0.664l4.123%2C3.663l0.797%2C0.708l0.656-0.84%20%20%20l7.027-9.002l0.615-0.788L15.963%2C9.55l-1.182-0.924L13.993%2C8.01l-0.616%2C0.789L7.988%2C15.701%20M19.523%2C5.867H0.01v17.125h1.701h1.5%20%20%20h3.328h1.5h3.493h1.5h3.328h1.5h1.662V5.867L19.523%2C5.867z%20M8.12%2C17.156l6.045-7.742l1.182%2C0.924L8.32%2C19.34l-4.123-3.663%20%20%20l0.996-1.121L8.12%2C17.156L8.12%2C17.156z%22%3E%3C%2Fpath%3E%3Cpolygon%20display%3D%22inline%22%20points%3D%2214.165%2C9.414%208.12%2C17.156%205.193%2C14.556%204.197%2C15.677%208.32%2C19.34%2015.347%2C10.338%20%20%20%20%20%22%3E%3C%2Fpolygon%3E%3C%2Fg%3E%3Cg%20display%3D%22none%22%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M18.295%2C32c8.837%2C0%2C16-7.163%2C16-16s-7.163-16-16-16s-16%2C7.163-16%2C16S9.459%2C32%2C18.295%2C32z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M16.992%2C24.633c0.911%2C0%2C1.651-0.735%2C1.664-1.642h-3.328%20%20%20C15.342%2C23.899%2C16.082%2C24.633%2C16.992%2C24.633z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M7.17%2C24.633c0.911%2C0%2C1.651-0.735%2C1.664-1.642H5.506C5.52%2C23.899%2C6.26%2C24.633%2C7.17%2C24.633%20%20%20z%22%3E%3C%2Fpath%3E%3Ccircle%20display%3D%22inline%22%20cx%3D%2228.827%22%20cy%3D%2222.967%22%20r%3D%221.67%22%3E%3C%2Fcircle%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M31.613%2C21.492L31.613%2C21.492l1.172%2C0v-4.597l-3.584-5.737h-4.373v10.333h1.212l0%2C0%20%20%20c0.532-1.002%2C1.573-1.692%2C2.786-1.692S31.081%2C20.49%2C31.613%2C21.492z%20M25.994%2C14.587v-2.297h2.52l1.25%2C2.297H25.994z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M20.156%2C22.992h-1.5c-0.014%2C0.907-0.754%2C1.642-1.664%2C1.642s-1.65-0.735-1.664-1.642h-1.5%20%20%20c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142S20.143%2C24.729%2C20.156%2C22.992z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M8.835%2C22.992c-0.014%2C0.907-0.754%2C1.642-1.664%2C1.642s-1.651-0.735-1.664-1.642h-1.5%20%20%20c0.014%2C1.737%2C1.424%2C3.142%2C3.164%2C3.142s3.151-1.405%2C3.164-3.142H8.835z%22%3E%3C%2Fpath%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M34.285%2C22.992v-6.527l-4.252-6.807h-6.704v13.333h2.334c0-0.008-0.003-0.016-0.003-0.025%20%20%20c0%2C1.749%2C1.418%2C3.167%2C3.167%2C3.167s3.167-1.418%2C3.167-3.167c0%2C0.008-0.002%2C0.016-0.003%2C0.025H34.285z%20M28.827%2C24.636%20%20%20c-0.922%2C0-1.67-0.747-1.67-1.67s0.748-1.67%2C1.67-1.67s1.67%2C0.747%2C1.67%2C1.67S29.749%2C24.636%2C28.827%2C24.636z%20M28.827%2C19.8%20%20%20c-1.213%2C0-2.254%2C0.69-2.786%2C1.692l0%2C0h-1.212V11.158h4.373l3.584%2C5.737v4.597h-1.172l0%2C0C31.081%2C20.49%2C30.04%2C19.8%2C28.827%2C19.8z%22%3E%3C%2Fpath%3E%3Cpolygon%20display%3D%22inline%22%20points%3D%2225.994%2C12.289%2025.994%2C14.587%2029.764%2C14.587%2028.514%2C12.289%20%20%22%3E%3C%2Fpolygon%3E%3Cpath%20display%3D%22inline%22%20d%3D%22M15.328%2C22.992h3.328h1.5h1.662V5.867H2.306v17.125h1.701h1.5h3.328h1.5h3.493H15.328z%20%20%20%20M10.615%2C19.34l-4.123-3.663l0.996-1.121l2.928%2C2.601l6.045-7.742l1.182%2C0.924L10.615%2C19.34z%22%3E%3C%2Fpath%3E%3Cpolygon%20display%3D%22inline%22%20points%3D%2216.461%2C9.414%2010.416%2C17.156%207.488%2C14.556%206.492%2C15.677%2010.615%2C19.34%20%20%20%2017.642%2C10.338%20%20%22%3E%3C%2Fpolygon%3E%3C%2Fg%3E%3C%2Fsvg%3E");
      }

      #GROUP3319 {
        width: 199px;
        height: 22px;
        top: 0px;
        left: 100.5px;
      }

      #BOX3324,
      #GROUP3323,
      #GROUP3322 {
        width: 372px;
        height: 82.217px;
      }

      #BOX3324>.ladi-box {
        border-width: 2px;
        border-radius: 99px;
        border-style: solid;
        border-color: rgb(224, 224, 224);
      }

      #BUTTON3325 {
        width: 152.338px;
        height: 47.9599px;
        top: 17.1285px;
        left: 26.228px;
      }

      #BUTTON3325>.ladi-button>.ladi-button-background,
      #BUTTON3040>.ladi-button>.ladi-button-background {
        background-image: linear-gradient(rgb(229, 45, 39), rgb(179, 18, 23));
        background-color: initial;
        background-size: initial;
        background-origin: initial;
        background-position: initial;
        background-repeat: initial;
        background-attachment: initial;
      }

      #BUTTON3325>.ladi-button>.ladi-button-background,
      #FORM3039 .ladi-form-item-background,
      #BUTTON3040>.ladi-button>.ladi-button-background {
        -webkit-background-clip: initial;
      }

      #BUTTON3325>.ladi-button {
        border-radius: 99px;
      }

      #BUTTON3325.ladi-animation>.ladi-button {
        animation-name: pulse;
        animation-delay: 0s;
        animation-duration: 1.5s;
        animation-iteration-count: infinite;
      }

      #BUTTON_TEXT3325 {
        width: 152px;
        top: 12.3325px;
        left: 0px;
      }

      #BUTTON_TEXT3325>.ladi-headline {
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #HEADLINE3328 {
        width: 170px;
        top: 10.9623px;
        left: 0px;
      }

      #HEADLINE3328>.ladi-headline {
        font-size: 28px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(232, 59, 48);
      }

      #HEADLINE3329 {
        width: 83px;
      }

      #HEADLINE3329>.ladi-headline {
        font-size: 12px;
        line-height: 1.6;
        color: rgb(132, 132, 132);
      }

      #GROUP3327 {
        width: 170px;
        height: 55.9623px;
        top: 5.4811px;
        left: 192.542px;
      }

      #HEADLINE3330 {
        width: 127px;
        top: 52px;
        left: 197px;
      }

      #HEADLINE3330>.ladi-headline {
        font-size: 19px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(192, 192, 192);
        text-decoration-line: line-through;
      }

      #GROUP3322 {
        top: 22px;
        left: 14px;
      }

      #GROUP3307 {
        width: 400px;
        height: 148.717px;
        top: 553.75px;
        left: 8.75px;
      }

      #HEADLINE2539,
      #HEADLINE3587 {
        width: 281px;
      }

      #HEADLINE2539 {
        top: 0px;
        left: 68.25px;
      }

      #HEADLINE2539>.ladi-headline {
        font-family: Oswald, sans-serif;
        font-size: 29px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(224, 224, 224);
        text-transform: uppercase;
        text-align: left;
        -webkit-text-stroke: 0px rgb(255, 255, 255);
      }

      #IMAGE3334 {
        width: 106.775px;
        height: 114.001px;
        top: 48px;
        left: 313.225px;
      }

      #IMAGE3334>.ladi-image>.ladi-image-background {
        width: 106.775px;
        height: 114.89px;
        background-image: url("https://w.ladicdn.com/s450x450/5dbe4694ed94dc587f3c244b/image-removebg-preview-43-20230222074600-sdmpm.png");
      }

      #IMAGE3334.ladi-animation>.ladi-image {
        animation-name: rubberBand;
        animation-delay: 1s;
        animation-duration: 1s;
        animation-iteration-count: 1;
      }

      #SECTION3335 {
        height: 785.12px;
      }

      #SECTION3335>.ladi-section-background,
      #SECTION3683>.ladi-section-background,
      #SECTION3691>.ladi-section-background,
      #SECTION3699>.ladi-section-background,
      #SECTION3692>.ladi-section-background,
      #SECTION3694>.ladi-section-background,
      #SECTION3696>.ladi-section-background,
      #SECTION3697>.ladi-section-background,
      #BOX2787>.ladi-box,
      #BOX2794>.ladi-box,
      #BOX2801>.ladi-box,
      #BOX2808>.ladi-box,
      #SECTION216>.ladi-overlay {
        background-color: rgb(0, 0, 0);
      }

      #BOX3683,
      #GROUP3686,
      #BOX3684,
      #GROUP3687,
      #BOX3698,
      #GROUP3704,
      #BOX3699,
      #GROUP3707,
      #BOX3700,
      #GROUP3708,
      #BOX3710,
      #GROUP3733,
      #BOX3701,
      #GROUP3709,
      #BOX3703,
      #GROUP3716,
      #BOX3704,
      #GROUP3718 {
        width: 395.5px;
        height: 46px;
      }

      #BOX3683>.ladi-box,
      #BOX3684>.ladi-box,
      #BOX3698>.ladi-box,
      #BOX3699>.ladi-box,
      #BOX3700>.ladi-box,
      #BOX3710>.ladi-box,
      #BOX3701>.ladi-box,
      #BOX3703>.ladi-box,
      #BOX3704>.ladi-box {
        border-radius: 60px;
      }

      #BOX3683>.ladi-box,
      #BOX3684>.ladi-box,
      #BOX3698>.ladi-box,
      #BOX3699>.ladi-box,
      #BOX3700>.ladi-box,
      #BOX3710>.ladi-box,
      #BOX3701>.ladi-box,
      #BOX3703>.ladi-box,
      #BOX3704>.ladi-box,
      #BUTTON1362>.ladi-button>.ladi-button-background,
      #BOX3592>.ladi-box {
        background-color: rgb(199, 31, 22);
      }

      #HEADLINE3342 {
        width: 347px;
        top: 5px;
        left: 24.25px;
      }

      #HEADLINE3342>.ladi-headline,
      #HEADLINE3687>.ladi-headline,
      #HEADLINE3711>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 31.67px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(224, 224, 224);
        text-align: left;
      }

      #HEADLINE3342>.ladi-headline,
      #HEADLINE3687>.ladi-headline,
      #HEADLINE3711>.ladi-headline,
      #HEADLINE3714>.ladi-headline,
      #HEADLINE3715>.ladi-headline,
      #HEADLINE3734>.ladi-headline,
      #HEADLINE3716>.ladi-headline,
      #HEADLINE3722>.ladi-headline,
      #HEADLINE3724>.ladi-headline {
        text-shadow: rgb(0, 0, 0) 1px 2px 3px;
      }

      #GROUP3686,
      #GROUP3687,
      #GROUP3704,
      #GROUP3707,
      #GROUP3708,
      #GROUP3709,
      #GROUP3716,
      #GROUP3718 {
        top: 11.176px;
        left: 10px;
      }

      #IMAGE3709 {
        width: 420px;
        height: 251.238px;
        top: 168.631px;
        left: 0px;
      }

      #IMAGE3709>.ladi-image>.ladi-image-background {
        width: 425.508px;
        height: 251.238px;
        top: 0px;
        left: -5.50744px;
        background-image: url("https://w.ladicdn.com/s750x600/5dbe4694ed94dc587f3c244b/o1cn013ygkak1ucvavgesc0_2214127126001-0-cib-20230523081819-uob7x.png");
      }

      #IMAGE3746,
      #IMAGE3746>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 355.25px;
      }

      #IMAGE3746 {
        top: 424.593px;
        left: 0px;
      }

      #IMAGE3746>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/5dbe4694ed94dc587f3c244b/giphy-2-20230523090440-_z7y3.gif");
      }

      #BOX3707,
      #GROUP3727,
      #BOX3531,
      #GROUP3726 {
        width: 420px;
        height: 90px;
      }

      #BOX3707>.ladi-box,
      #BOX3531>.ladi-box {
        border-width: 3px;
        border-radius: 40px;
        border-style: dotted;
        border-color: rgb(255, 188, 1);
      }

      #LIST_PARAGRAPH3684,
      #LIST_PARAGRAPH3530 {
        width: 402px;
        top: 7.862px;
        left: 9px;
      }

      #LIST_PARAGRAPH3684>.ladi-list-paragraph {
        font-size: 15px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
      }

      #LIST_PARAGRAPH3684 ul li,
      #LIST_PARAGRAPH3530 ul li {
        padding-left: 24px;
      }

      #LIST_PARAGRAPH3684 ul li:before,
      #LIST_PARAGRAPH3530 ul li:before {
        width: 20px;
        height: 20px;
        top: 3px;
      }

      #LIST_PARAGRAPH3684 ul li:before {
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22100%25%22%20height%3D%22100%25%22%20%20viewBox%3D%220%200%201792%201896.0833%22%20class%3D%22%22%20fill%3D%22rgba(191%2C%202%2C%202%2C%201)%22%3E%20%3Cpath%20d%3D%22M1671%20566q0%2040-28%2068l-724%20724-136%20136q-28%2028-68%2028t-68-28l-136-136-362-362q-28-28-28-68t28-68l136-136q28-28%2068-28t68%2028l294%20295%20656-657q28-28%2068-28t68%2028l136%20136q28%2028%2028%2068z%22%3E%3C%2Fpath%3E%20%3C%2Fsvg%3E");
      }

      #IMAGE3758,
      #IMAGE3758>.ladi-image>.ladi-image-background,
      #IMAGE3532,
      #IMAGE3532>.ladi-image>.ladi-image-background {
        width: 61.862px;
        height: 61.862px;
      }

      #IMAGE3758,
      #IMAGE3532 {
        top: 4px;
        left: 340.138px;
      }

      #IMAGE3758>.ladi-image>.ladi-image-background,
      #IMAGE3532>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/image-removebg-preview-26-copy-20221121045207-fojzh.png");
      }

      #GROUP3727 {
        top: 66.685px;
        left: 0px;
      }

      #BOX3711 {
        width: 64px;
        height: 16px;
        top: 256.843px;
        left: 10px;
      }

      #BOX3711>.ladi-box,
      #BOX3709>.ladi-box,
      #IMAGE1360>.ladi-image {
        border-radius: 0px;
      }

      #BOX3711>.ladi-box,
      #SECTION3693>.ladi-section-background,
      #BOX2784>.ladi-box,
      #BOX2791>.ladi-box,
      #BOX2798>.ladi-box,
      #BOX2805>.ladi-box,
      #SECTION1356>.ladi-section-background,
      #FRAME243>.ladi-frame>.ladi-frame-background {
        background-color: rgb(255, 255, 255);
      }

      #HEADLINE3735 {
        width: 42px;
        top: 256.843px;
        left: 15px;
      }

      #HEADLINE3735>.ladi-headline {
        font-size: 9.93px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: left;
      }

      #SECTION3683 {
        height: 689.11px;
      }

      #HEADLINE3687,
      #HEADLINE3711,
      #HEADLINE3714 {
        width: 342px;
        top: 4px;
        left: 26.75px;
      }

      #IMAGE3721 {
        width: 420px;
        height: 234.683px;
        top: 98.75px;
        left: 0px;
      }

      #IMAGE3721>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 370.383px;
        top: -77.7px;
        left: 0px;
        background-image: url("https://w.ladicdn.com/s750x700/5dbe4694ed94dc587f3c244b/new-1-20230313080613-odk4a-20230523081759-du3ik.jpg");
      }

      #SHAPE3687,
      #SHAPE3691 {
        width: 28.9643px;
        height: 30.6466px;
      }

      #HEADLINE3693,
      #HEADLINE3710 {
        width: 372px;
        top: 0px;
        left: 35px;
      }

      #HEADLINE3693>.ladi-headline,
      #HEADLINE3710>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 18px;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: justify;
      }

      #GROUP3691 {
        width: 407px;
        height: 30.6466px;
        top: 64.184px;
        left: 0px;
      }

      #IMAGE3722 {
        width: 420px;
        height: 287.441px;
        top: 396.184px;
        left: 0px;
      }

      #IMAGE3722>.ladi-image>.ladi-image-background {
        width: 458.595px;
        height: 400.954px;
        top: -43.1351px;
        left: -15.8919px;
        background-image: url("https://w.ladicdn.com/s800x750/5dbe4694ed94dc587f3c244b/new-4-20230313080613-1swu3-20230523081759-2gshz.jpg");
      }

      #GROUP3703 {
        width: 407px;
        height: 58px;
        top: 338.184px;
        left: 0px;
      }

      #SECTION3691 {
        height: 1213.11px;
      }

      #IMAGE3725,
      #IMAGE3725>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 555.985px;
      }

      #IMAGE3725 {
        top: 654.286px;
        left: 0px;
      }

      #IMAGE3725>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x900/5dbe4694ed94dc587f3c244b/siucap9-20230331042444-7lp7f-20230523094749-awd-i.jpg");
      }

      #IMAGE3748,
      #IMAGE3748>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 593.04px;
      }

      #IMAGE3748 {
        top: 61.246px;
        left: 0px;
      }

      #IMAGE3748>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x900/5dbe4694ed94dc587f3c244b/siucap8-20230331042444-qlnrr-20230523094811-ouwbg.jpg");
      }

      #SECTION3699 {
        height: 1201.11px;
      }

      #LIST_PARAGRAPH3685,
      #LINE2782 {
        width: 393px;
      }

      #LIST_PARAGRAPH3685 {
        top: 55.226px;
        left: 13.5px;
      }

      #LIST_PARAGRAPH3685>.ladi-list-paragraph {
        font-size: 15px;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: justify;
      }

      #LIST_PARAGRAPH3685 ul li {
        padding-left: 15px;
      }

      #LIST_PARAGRAPH3685 ul li:before {
        top: -4px;
        font-size: 20px;
        color: rgb(191, 2, 2);
        content: counter(linum, square);
      }

      #HEADLINE3731 {
        width: 295px;
        top: 6.226px;
        left: 62.5px;
      }

      #HEADLINE3731>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 26px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 188, 1);
        text-decoration-line: underline;
        text-align: left;
      }

      #VIDEO3683 {
        width: 420px;
        height: 236.25px;
        top: 158.226px;
        left: 0px;
      }

      #VIDEO3683>.ladi-video>.ladi-video-background {
        background-image: url("https://w.ladicdn.com/s420x236/57b167c9ca57d39c18a1c57c/omguzjrr20220811072626.jpg");
        background-size: cover;
        background-origin: content-box;
        background-position: 50% 50%;
        background-repeat: no-repeat;
        background-attachment: scroll;
      }

      #SHAPE3683 {
        width: 40px;
        height: 40px;
        top: 98.125px;
        left: 190px;
      }

      #SHAPE3683 svg:last-child {
        fill: rgba(0, 0, 0, 0.5);
      }

      #IMAGE3760 {
        width: 420px;
        height: 311.5px;
        top: 398.817px;
        left: 0px;
      }

      #IMAGE3760>.ladi-image>.ladi-image-background {
        width: 518.519px;
        height: 399.648px;
        top: -44.0741px;
        left: 2.59259px;
        background-image: url("https://w.ladicdn.com/s850x700/5dbe4694ed94dc587f3c244b/hqdefault-20230525031609-dkx_t.jpg");
      }

      #HEADLINE3732,
      #HEADLINE3733 {
        width: 324px;
        top: 6.14px;
        left: 36px;
      }

      #HEADLINE3732>.ladi-headline,
      #HEADLINE3733>.ladi-headline {
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: left;
      }

      #SHAPE3698,
      #SHAPE3699 {
        width: 30.6536px;
        height: 41.2797px;
      }

      #SHAPE3698 svg:last-child {
        fill: rgb(191, 2, 2);
      }

      #GROUP3731,
      #GROUP3732 {
        width: 360px;
        height: 41.2797px;
      }

      #GROUP3731 {
        top: 404.253px;
        left: 22px;
      }

      #IMAGE3761,
      #IMAGE3761>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 482.16px;
      }

      #IMAGE3761,
      #BOX3709 {
        top: 715.093px;
        left: 0px;
      }

      #BOX3709 {
        width: 420px;
        height: 51px;
      }

      #BOX3709>.ladi-box {
        background-color: rgb(241, 243, 244);
      }

      #SHAPE3699 svg:last-child {
        fill: rgb(3, 255, 111);
      }

      #GROUP3732 {
        top: 719.953px;
        left: 27.75px;
      }

      #SECTION3692 {
        height: 802.11px;
      }

      #HEADLINE3714>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 31.67px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(224, 224, 224);
        text-align: center;
      }

      #IMAGE3727,
      #IMAGE3727>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 488.88px;
      }

      #IMAGE3727 {
        top: 62.436px;
        left: 0px;
      }

      #IMAGE3757,
      #IMAGE3757>.ladi-image>.ladi-image-background {
        width: 420.383px;
        height: 242.55px;
      }

      #IMAGE3757 {
        top: 555.316px;
        left: 0.2055px;
      }

      #IMAGE3757>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x550/5dbe4694ed94dc587f3c244b/ct15-20221229064929-nneno-20230523102600-rjrsm.jpg");
      }

      #HEADLINE3729 {
        width: 103px;
        top: 96.116px;
        left: 279px;
      }

      #HEADLINE3729>.ladi-headline {
        font-size: 17.68px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: left;
      }

      #SECTION3693 {
        height: 1111.17px;
      }

      #HEADLINE3715,
      #HEADLINE229 {
        width: 369px;
      }

      #HEADLINE3715 {
        top: 6px;
        left: 13.25px;
      }

      #HEADLINE3715>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 28px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(224, 224, 224);
        text-align: center;
      }

      #IMAGE3728,
      #IMAGE3728>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 531.88px;
      }

      #IMAGE3728 {
        top: 57.176px;
        left: 0.2055px;
      }

      #IMAGE3728>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x850/5dbe4694ed94dc587f3c244b/o1cn01qkzt6r2frmojp6qd6_2210971298933-0-cib-20230523081819-eubn7.png");
      }

      #HEADLINE3734,
      #HEADLINE3716,
      #HEADLINE3722,
      #HEADLINE3724 {
        width: 371px;
        top: 8.5px;
        left: 14.5px;
      }

      #HEADLINE3734>.ladi-headline,
      #HEADLINE3716>.ladi-headline,
      #HEADLINE3722>.ladi-headline,
      #HEADLINE3724>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 24px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(224, 224, 224);
        text-align: center;
      }

      #GROUP3733 {
        top: 582.176px;
        left: 12.25px;
      }

      #IMAGE3762 {
        width: 484.548px;
        height: 329.74px;
        top: 595.413px;
        left: -39px;
      }

      #IMAGE3762>.ladi-image>.ladi-image-background {
        width: 484.548px;
        height: 380.74px;
        background-image: url("https://w.ladicdn.com/s800x700/5dbe4694ed94dc587f3c244b/siucap10-20230401083537-bj7yi-20230525032357-sib6y.png");
      }

      #IMAGE3763 {
        width: 499.231px;
        height: 212.017px;
        top: 899.153px;
        left: -54.755px;
      }

      #IMAGE3763>.ladi-image>.ladi-image-background {
        width: 499.231px;
        height: 411.907px;
      }

      #IMAGE3763>.ladi-image>.ladi-image-background,
      #IMAGE3765>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s800x750/5dbe4694ed94dc587f3c244b/siucap11-20230401083538-du_hx-20230525032357-7edb7.png");
      }

      #IMAGE3765 {
        width: 236.231px;
        height: 164.017px;
        top: 939.153px;
        left: 85.1585px;
      }

      #IMAGE3765>.ladi-image>.ladi-image-background {
        width: 499.231px;
        height: 412.017px;
        top: -219px;
        left: -27px;
      }

      #SECTION3694 {
        height: 855.11px;
      }

      #IMAGE3729 {
        width: 420px;
        height: 311.701px;
        top: 33.32px;
        left: 0px;
      }

      #IMAGE3729>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 313.155px;
        background-image: url("https://w.ladicdn.com/s750x650/5dbe4694ed94dc587f3c244b/o1cn011qiegm2frmoc4amwa_2210971298933-0-cib-20230523081830-tyqjc.png");
      }

      #HEADLINE3717 {
        width: 204px;
        top: 0px;
        left: 105.75px;
      }

      #HEADLINE3717>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 18px;
        font-weight: bold;
        font-style: italic;
        line-height: 1.6;
        color: rgb(10, 103, 233);
        text-align: left;
      }

      #GROUP3710 {
        width: 420px;
        height: 345.021px;
        top: 64.116px;
        left: 0px;
      }

      #IMAGE3731,
      #IMAGE3731>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 436.8px;
      }

      #IMAGE3731 {
        top: 412.616px;
        left: 0px;
      }

      #IMAGE3731>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x750/5dbe4694ed94dc587f3c244b/o1cn012ixiar2frmoidra4a_2210971298933-0-cib-20230523081820-g6o4m.png");
      }

      #SECTION3696 {
        height: 543.11px;
      }

      #IMAGE3747,
      #IMAGE3747>.ladi-image>.ladi-image-background {
        width: 419.902px;
        height: 355.167px;
      }

      #IMAGE3747 {
        top: 185.948px;
        left: 0px;
      }

      #IMAGE3747>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/5dbe4694ed94dc587f3c244b/giphy-1-20230523090524-fksnq.gif");
      }

      #IMAGE3738,
      #IMAGE3738>.ladi-image>.ladi-image-background {
        width: 138.268px;
        height: 120.024px;
      }

      #IMAGE3738>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s450x450/5dbe4694ed94dc587f3c244b/t6rz-20221229023257-vqijo-20230523081759--naql.jpg");
      }

      #IMAGE3741 {
        width: 138.278px;
        height: 120.024px;
        top: 0px;
        left: 138.921px;
      }

      #IMAGE3741>.ladi-image>.ladi-image-background {
        width: 138.278px;
        height: 120.033px;
        background-image: url("https://w.ladicdn.com/s450x450/5dbe4694ed94dc587f3c244b/t8-20230328104913-trct2-20230523081759-pj_rg.jpg");
      }

      #GROUP3719 {
        width: 277.199px;
        height: 120.024px;
      }

      #IMAGE3749,
      #IMAGE3749>.ladi-image>.ladi-image-background {
        width: 142.108px;
        height: 120.024px;
      }

      #IMAGE3749 {
        top: 0px;
        left: 277.794px;
      }

      #IMAGE3749>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s450x450/5dbe4694ed94dc587f3c244b/t7-20230328104913-upwr-20230523102457-e82uq.jpg");
      }

      #GROUP3722 {
        width: 419.902px;
        height: 120.024px;
        top: 64.9239px;
        left: 0px;
      }

      #SECTION3697 {
        height: 487.11px;
      }

      #IMAGE3745,
      #IMAGE3745>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 420px;
      }

      #IMAGE3745 {
        top: 62.6195px;
        left: 0px;
      }

      #IMAGE3745>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x750/5dbe4694ed94dc587f3c244b/ct17-20221229070645-6j5zz-20230523081759-fy279.jpg");
      }

      #SECTION2776 {
        height: 492.197px;
      }

      #HEADLINE2777 {
        top: 0px;
        left: 10px;
      }

      #HEADLINE2777>.ladi-headline {
        font-family: Quicksand, sans-serif;
        font-size: 26px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(22, 169, 199);
        text-transform: uppercase;
        text-align: center;
      }

      #BOX2784,
      #GROUP2783,
      #BOX2791,
      #GROUP2790,
      #BOX2798,
      #GROUP2797,
      #BOX2805,
      #GROUP2804 {
        width: 173.301px;
        height: 208px;
      }

      #BOX2784>.ladi-box,
      #BOX2791>.ladi-box,
      #BOX2798>.ladi-box,
      #BOX2805>.ladi-box {
        border-radius: 50px;
        box-shadow: rgba(0, 0, 0, 0.2) 0px 0px 30px -10px;
      }

      #HEADLINE2785,
      #HEADLINE2799 {
        width: 126px;
        top: 123.859px;
        left: 23.5492px;
      }

      #HEADLINE2785>.ladi-headline,
      #HEADLINE2792>.ladi-headline,
      #HEADLINE2799>.ladi-headline,
      #HEADLINE2806>.ladi-headline {
        font-size: 13px;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: center;
      }

      #HEADLINE2786 {
        width: 101px;
        top: 88.809px;
        left: 36.4032px;
      }

      #HEADLINE2786>.ladi-headline,
      #HEADLINE2793>.ladi-headline,
      #HEADLINE2800>.ladi-headline,
      #HEADLINE2807>.ladi-headline {
        font-family: Montserrat, sans-serif;
        font-size: 18px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(64, 64, 64);
        text-transform: uppercase;
        text-align: center;
      }

      #BOX2787,
      #BOX2794,
      #BOX2801,
      #BOX2808 {
        width: 56.8363px;
        height: 56.8362px;
        top: 21.3578px;
        left: 58.2323px;
      }

      #BOX2787>.ladi-box,
      #BOX2794>.ladi-box,
      #BOX2801>.ladi-box,
      #BOX2808>.ladi-box,
      #IMAGE3608>.ladi-image,
      #BOX3610>.ladi-box,
      #IMAGE3640>.ladi-image,
      #BOX3682>.ladi-box,
      #IMAGE3616>.ladi-image,
      #BOX3632>.ladi-box,
      #BUTTON3040>.ladi-button,
      #BOX3490>.ladi-box {
        border-radius: 1000px;
      }

      #BOX2788,
      #BOX2795,
      #BOX2802,
      #BOX2809 {
        width: 52.5059px;
        height: 52.5058px;
        top: 23.5227px;
        left: 60.3976px;
      }

      #BOX2788>.ladi-box,
      #BOX2795>.ladi-box,
      #BOX2802>.ladi-box,
      #BOX2809>.ladi-box {
        border-width: 1px;
        border-radius: 1000px;
        border-style: solid;
        border-color: rgb(252, 160, 160);
      }

      #SHAPE2789,
      #SHAPE2796,
      #SHAPE2803,
      #SHAPE2810 {
        width: 25.9823px;
        height: 25.9822px;
      }

      #SHAPE2789,
      #SHAPE2796 {
        top: 36.7848px;
        left: 73.5581px;
      }

      #GROUP2783 {
        top: 60.197px;
        left: 20.5px;
      }

      #GROUP2783.ladi-animation>.ladi-group,
      #GROUP2790.ladi-animation>.ladi-group,
      #GROUP2797.ladi-animation>.ladi-group,
      #GROUP2804.ladi-animation>.ladi-group {
        animation-name: bounceInLeft;
        animation-delay: 1s;
        animation-duration: 1s;
        animation-iteration-count: 1;
      }

      #HEADLINE2792 {
        width: 140px;
        top: 123.859px;
        left: 16.5492px;
      }

      #HEADLINE2793,
      #HEADLINE2807,
      #HEADLINE238 {
        width: 132px;
      }

      #HEADLINE2793,
      #HEADLINE2807 {
        top: 89.5px;
        left: 20.5492px;
      }

      #GROUP2790 {
        top: 60.197px;
        left: 207.5px;
      }

      #HEADLINE2800 {
        width: 130px;
        top: 89.5px;
        left: 21.5492px;
      }

      #SHAPE2803,
      #SHAPE2810 {
        top: 36.7848px;
        left: 73.6594px;
      }

      #GROUP2797 {
        top: 272.197px;
        left: 20.9959px;
      }

      #HEADLINE2806 {
        width: 141px;
        top: 123.859px;
        left: 20.5492px;
      }

      #GROUP2804 {
        top: 272.197px;
        left: 207.5px;
      }

      #HEADLINE2781 {
        width: 392px;
      }

      #HEADLINE2781>.ladi-headline {
        font-family: "Open Sans", sans-serif;
        font-size: 30px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(199, 31, 22);
        text-align: center;
      }

      #LINE2782 {
        top: 32px;
        left: 5.2457px;
      }

      #LINE2782>.ladi-line>.ladi-line-container {
        border-top: 2px solid rgb(255, 188, 1);
        border-right: 2px solid rgb(255, 188, 1);
        border-bottom: 2px solid rgb(255, 188, 1);
        border-left: 0px !important;
      }

      #LINE2782>.ladi-line,
      #LINE3586>.ladi-line,
      #LINE217>.ladi-line {
        width: 100%;
        padding: 8px 0px;
      }

      #GROUP2780 {
        width: 398.246px;
        height: 50px;
        top: 7px;
        left: 14px;
      }

      #SECTION1356 {
        height: 443.708px;
      }

      #IMAGE1360 {
        width: 420px;
        height: 219.951px;
        top: 219.001px;
        left: -1.5px;
      }

      #IMAGE1360>.ladi-image>.ladi-image-background {
        width: 460.621px;
        height: 233.759px;
        top: -13.8082px;
        left: -40.6197px;
        background-image: url("https://w.ladicdn.com/s800x550/5dbe4694ed94dc587f3c244b/letan6-1024x538-20220225074626.png");
      }

      #HEADLINE1361 {
        width: 344px;
        top: 5px;
        left: 79.4995px;
      }

      #HEADLINE1361>.ladi-headline {
        font-family: "Open Sans", sans-serif;
        font-size: 25px;
        font-weight: bold;
        line-height: 1.4;
        color: rgb(0, 0, 0);
        letter-spacing: 0px;
        text-align: left;
      }

      #BUTTON1362 {
        width: 155.999px;
        height: 35px;
        top: 180.001px;
        left: 132px;
      }

      #BUTTON1362>.ladi-button {
        border-width: 1px;
        border-radius: 67px;
        border-style: solid;
        border-color: rgb(255, 255, 255);
      }

      #BUTTON_TEXT1362 {
        width: 155px;
        top: 9px;
        left: 0px;
      }

      #BUTTON_TEXT1362>.ladi-headline {
        font-size: 22px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #HEADLINE1364 {
        width: 408px;
        top: 72.049px;
        left: 7.5px;
      }

      #HEADLINE1364>.ladi-headline {
        font-family: Tinos, serif;
        font-size: 15px;
        line-height: 1.4;
        color: rgb(5, 5, 5);
        text-align: justify;
      }

      #IMAGE1367,
      #IMAGE1367>.ladi-image>.ladi-image-background {
        width: 78.1607px;
        height: 74.7377px;
      }

      #IMAGE1367 {
        top: 2.63115px;
        left: 0px;
      }

      #IMAGE1367>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/tmax-do-phan-giai-24-20230720024532-xmgnj.png");
      }

      #SECTION3585 {
        height: 1430.89px;
      }

      #LINE3586 {
        width: 420px;
        top: 73.058px;
        left: 0px;
      }

      #LINE3586>.ladi-line>.ladi-line-container {
        border-top: 1px solid rgb(211, 211, 211);
        border-right: 1px solid rgb(211, 211, 211);
        border-bottom: 1px solid rgb(211, 211, 211);
        border-left: 0px !important;
      }

      #HEADLINE3587 {
        top: 43.21px;
        left: 0px;
      }

      #HEADLINE3587>.ladi-headline {
        font-size: 19px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(92, 92, 92);
        text-align: left;
      }

      #HEADLINE3588 {
        width: 226px;
        top: 15.6595px;
        left: 0px;
      }

      #HEADLINE3588>.ladi-headline {
        font-size: 15px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(199, 31, 22);
        text-align: left;
      }

      #HEADLINE3590 {
        width: 125px;
        top: 9.601px;
        left: 0px;
      }

      #HEADLINE3590>.ladi-headline {
        font-size: 15px;
        line-height: 1.2;
        color: rgb(60, 60, 60);
        text-align: left;
      }

      #SHAPE3591 {
        width: 12.6768px;
        height: 22.7807px;
        top: 7.601px;
        left: 227.758px;
      }

      #SHAPE3591 svg:last-child {
        fill: rgb(95, 95, 95);
      }

      #BOX3592 {
        width: 140px;
        height: 37.101px;
        top: 0px;
        left: 111.308px;
      }

      #BOX3592>.ladi-box {
        border-width: 1px;
        border-radius: 4px;
        border-style: solid;
        border-color: rgb(191, 191, 191);
      }

      #HEADLINE3593 {
        width: 89px;
        top: 10.601px;
        left: 133.493px;
      }

      #HEADLINE3593>.ladi-headline {
        font-size: 15px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(255, 252, 250);
        text-align: left;
      }

      #GROUP3589 {
        width: 251.308px;
        height: 37.101px;
        top: 22.109px;
        left: 168.692px;
      }

      #SHAPE3596,
      #SHAPE3597,
      #SHAPE3598,
      #SHAPE3599,
      #SHAPE3600,
      #SHAPE3650,
      #SHAPE3651,
      #SHAPE3652,
      #SHAPE3653,
      #SHAPE3654,
      #SHAPE3622,
      #SHAPE3623,
      #SHAPE3624,
      #SHAPE3625,
      #SHAPE3626 {
        width: 11.6684px;
        height: 13.296px;
      }

      #SHAPE3597,
      #SHAPE3651,
      #SHAPE3623 {
        top: 0px;
        left: 12.8922px;
      }

      #SHAPE3598,
      #SHAPE3652,
      #SHAPE3624 {
        top: 0px;
        left: 25.7843px;
      }

      #SHAPE3599,
      #SHAPE3653,
      #SHAPE3625 {
        top: 0px;
        left: 38.6765px;
      }

      #SHAPE3600,
      #SHAPE3654,
      #SHAPE3626 {
        top: 0px;
        left: 51.5686px;
      }

      #GROUP3595,
      #GROUP3649,
      #GROUP3621 {
        width: 63.237px;
        height: 13.296px;
      }

      #PARAGRAPH3601,
      #PARAGRAPH3604,
      #PARAGRAPH3656,
      #PARAGRAPH3659,
      #PARAGRAPH3627,
      #PARAGRAPH3630 {
        width: 163px;
      }

      #PARAGRAPH3601 {
        top: 20.296px;
        left: 0px;
      }

      #PARAGRAPH3601>.ladi-paragraph,
      #PARAGRAPH3604>.ladi-paragraph,
      #PARAGRAPH3613>.ladi-paragraph,
      #PARAGRAPH3647>.ladi-paragraph,
      #PARAGRAPH3656>.ladi-paragraph,
      #PARAGRAPH3659>.ladi-paragraph,
      #PARAGRAPH3627>.ladi-paragraph,
      #PARAGRAPH3630>.ladi-paragraph,
      #PARAGRAPH3635>.ladi-paragraph {
        font-size: 13px;
        line-height: 1.2;
        color: rgb(67, 102, 176);
        text-align: left;
      }

      #IMAGE3602,
      #IMAGE3602>.ladi-image>.ladi-image-background,
      #IMAGE3657,
      #IMAGE3657>.ladi-image>.ladi-image-background,
      #IMAGE3628,
      #IMAGE3628>.ladi-image>.ladi-image-background {
        width: 23.7439px;
        height: 25.9772px;
      }

      #IMAGE3602 {
        top: 13.296px;
        left: 111.855px;
      }

      #IMAGE3602>.ladi-image>.ladi-image-background,
      #IMAGE3657>.ladi-image>.ladi-image-background,
      #IMAGE3628>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s350x350/5c7362c6c417ab07e5196b05/df-20191029092328.png");
      }

      #PARAGRAPH3603 {
        width: 25px;
        top: 21.296px;
        left: 137.64px;
      }

      #PARAGRAPH3603>.ladi-paragraph,
      #PARAGRAPH3658>.ladi-paragraph,
      #PARAGRAPH3629>.ladi-paragraph {
        font-family: Montserrat, sans-serif;
        font-size: 12px;
        line-height: 1.2;
        color: rgb(75, 75, 75);
        text-align: left;
      }

      #PARAGRAPH3604 {
        top: 20.296px;
        left: 157.64px;
      }

      #GROUP3594 {
        width: 320.64px;
        height: 39.2732px;
        top: 489.448px;
        left: 89.3868px;
      }

      #PARAGRAPH3606 {
        width: 323px;
        top: 24.9995px;
        left: 82.2497px;
      }

      #PARAGRAPH3606>.ladi-paragraph,
      #PARAGRAPH3611>.ladi-paragraph,
      #PARAGRAPH3641>.ladi-paragraph,
      #PARAGRAPH3645>.ladi-paragraph,
      #PARAGRAPH3617>.ladi-paragraph,
      #PARAGRAPH3633>.ladi-paragraph {
        font-size: 15px;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: justify;
      }

      #PARAGRAPH3607 {
        width: 190px;
        top: 0px;
        left: 82.2498px;
      }

      #PARAGRAPH3607>.ladi-paragraph,
      #PARAGRAPH3642>.ladi-paragraph,
      #PARAGRAPH3618>.ladi-paragraph {
        font-size: 16px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(60, 60, 60);
        text-align: left;
      }

      #IMAGE3608 {
        width: 67.2683px;
        height: 70.258px;
        top: 1.704px;
        left: 0px;
      }

      #IMAGE3608>.ladi-image>.ladi-image-background {
        width: 85.8746px;
        height: 89.6911px;
        top: -8.96911px;
        left: -7.1562px;
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/13406997_285479828465203_3455325192652595021_n-20201211072550-20210826071208.jpg");
      }

      #GROUP3605 {
        width: 405.25px;
        height: 96.9995px;
        top: 95.058px;
        left: 5.37545px;
      }

      #BOX3610,
      #BOX3682,
      #BOX3632 {
        width: 56px;
        height: 56px;
      }

      #BOX3610,
      #BOX3632 {
        top: 4px;
        left: 0px;
      }

      #BOX3610>.ladi-box,
      #BOX3682>.ladi-box,
      #BOX3632>.ladi-box {
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/siucap1-removebg-20230331021854-qhjsw-20230523081924-bhfwj.png");
        background-size: cover;
        background-origin: content-box;
        background-position: 50% 0%;
        background-repeat: repeat;
        background-attachment: scroll;
      }

      #PARAGRAPH3611 {
        width: 254px;
        top: 22px;
        left: 79.7498px;
      }

      #PARAGRAPH3612,
      #PARAGRAPH3646,
      #PARAGRAPH3634 {
        width: 300px;
      }

      #PARAGRAPH3612,
      #PARAGRAPH3634 {
        top: 0px;
        left: 79.7498px;
      }

      #PARAGRAPH3612>.ladi-paragraph,
      #PARAGRAPH3646>.ladi-paragraph,
      #PARAGRAPH3634>.ladi-paragraph {
        font-size: 16px;
        font-weight: bold;
        line-height: 1.4;
        color: rgb(60, 60, 60);
        text-align: left;
      }

      #PARAGRAPH3613,
      #PARAGRAPH3614,
      #PARAGRAPH3647,
      #PARAGRAPH3648,
      #PARAGRAPH3635,
      #PARAGRAPH3636 {
        width: 166px;
      }

      #PARAGRAPH3613 {
        top: 83px;
        left: 79.7498px;
      }

      #PARAGRAPH3614 {
        top: 83px;
        left: 146.75px;
      }

      #PARAGRAPH3614>.ladi-paragraph,
      #PARAGRAPH3648>.ladi-paragraph,
      #PARAGRAPH3636>.ladi-paragraph {
        font-size: 13px;
        line-height: 1.2;
        color: rgb(93, 93, 93);
        text-align: left;
      }

      #GROUP3609 {
        width: 379.75px;
        height: 99px;
        top: 545.721px;
        left: 9px;
      }

      #IMAGE3637 {
        width: 212.84px;
        height: 431.685px;
        top: 208.057px;
        left: 89.3868px;
      }

      #IMAGE3637>.ladi-image>.ladi-image-background {
        width: 194.453px;
        height: 431.685px;
        top: -136px;
        left: 1px;
        background-image: url("https://w.ladicdn.com/s500x750/5dbe4694ed94dc587f3c244b/vn-11134103-22070-1scfqwa46leveb-20230720033618-_awdd.jpeg");
      }

      #IMAGE3640 {
        width: 68.3986px;
        height: 68.704px;
        top: 1.5px;
        left: 0px;
      }

      #IMAGE3640>.ladi-image>.ladi-image-background {
        width: 72.8246px;
        height: 73.15px;
        top: -0.683986px;
        left: -2.39395px;
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/93864612_1157436184594406_2087417014241984512_o-20200904023444.jpg");
      }

      #PARAGRAPH3641 {
        width: 319px;
        top: 27px;
        left: 82.9452px;
      }

      #PARAGRAPH3642 {
        width: 209px;
        top: 0px;
        left: 82.9452px;
      }

      #GROUP3639 {
        width: 401.945px;
        height: 75px;
      }

      #PARAGRAPH3645 {
        width: 237px;
        top: 22px;
        left: 0px;
      }

      #PARAGRAPH3647 {
        top: 83px;
        left: 0px;
      }

      #PARAGRAPH3648 {
        top: 83px;
        left: 67px;
      }

      #GROUP3643 {
        width: 300px;
        height: 99px;
        top: 131.273px;
        left: 85.5938px;
      }

      #GROUP3649 {
        top: 81.9952px;
        left: 85.5938px;
      }

      #PARAGRAPH3656 {
        top: 7px;
        left: 0px;
      }

      #IMAGE3657 {
        top: 0px;
        left: 114.216px;
      }

      #PARAGRAPH3658,
      #PARAGRAPH3629 {
        width: 48px;
      }

      #PARAGRAPH3658 {
        top: 8px;
        left: 141.001px;
      }

      #PARAGRAPH3659 {
        top: 7px;
        left: 154.001px;
      }

      #GROUP3655 {
        width: 317.001px;
        height: 25.9772px;
        top: 95.2912px;
        left: 85.5938px;
      }

      #GROUP3638,
      #GROUP3684 {
        width: 402.595px;
        height: 230.273px;
      }

      #BOX3682 {
        top: 144.24px;
        left: 0px;
      }

      #GROUP3684 {
        top: 673.309px;
        left: 9px;
      }

      #IMAGE3616 {
        width: 69.2289px;
        height: 66.7262px;
      }

      #IMAGE3616,
      #GROUP3726 {
        top: 2px;
        left: 0px;
      }

      #IMAGE3616>.ladi-image>.ladi-image-background {
        width: 74.8968px;
        height: 70.0254px;
        top: -1.51406px;
        left: -3.02813px;
        background-image: url("https://w.ladicdn.com/s400x400/5dbe4694ed94dc587f3c244b/96557806_242966723476052_8724218063064399872_o-20201211072805-20210826071208.jpg");
      }

      #PARAGRAPH3617 {
        width: 315px;
        top: 19px;
        left: 80.7891px;
      }

      #PARAGRAPH3618 {
        width: 588px;
        top: 0px;
        left: 80.7891px;
      }

      #GROUP3615 {
        width: 668.789px;
        height: 68.7262px;
      }

      #PARAGRAPH3627 {
        top: 22.157px;
        left: 0px;
      }

      #IMAGE3628 {
        top: 13.296px;
        left: 117.352px;
      }

      #PARAGRAPH3629 {
        top: 22.157px;
        left: 141.096px;
      }

      #GROUP3620 {
        width: 189.096px;
        height: 39.2732px;
      }

      #PARAGRAPH3630 {
        top: 22.157px;
        left: 182.695px;
      }

      #GROUP3619 {
        width: 345.695px;
        height: 39.2732px;
        top: 357.862px;
        left: 80.3868px;
      }

      #PARAGRAPH3633 {
        width: 576px;
        top: 26px;
        left: 79.7498px;
      }

      #PARAGRAPH3635 {
        top: 60px;
        left: 79.7498px;
      }

      #PARAGRAPH3636 {
        top: 60px;
        left: 145.75px;
      }

      #GROUP3631 {
        width: 655.75px;
        height: 76px;
        top: 411.862px;
        left: 0px;
      }

      #IMAGE3719,
      #IMAGE3719>.ladi-image>.ladi-image-background {
        width: 210.658px;
        height: 279.787px;
      }

      #IMAGE3719 {
        top: 75px;
        left: 80.3868px;
      }

      #IMAGE3719>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s550x600/5dbe4694ed94dc587f3c244b/786c1a2f4b4e1623f37e9eea9218d662-1-20230720033618-j1ya1.jpeg");
      }

      #GROUP3702 {
        width: 668.789px;
        height: 487.862px;
        top: 929.268px;
        left: 9px;
      }

      #SECTION3024 {
        height: 1090.09px;
      }

      #IMAGE3128,
      #IMAGE3128>.ladi-image>.ladi-image-background {
        width: 420px;
        height: 421.983px;
      }

      #IMAGE3128 {
        top: 194.112px;
        left: 0px;
      }

      #IMAGE3128>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s750x750/5dbe4694ed94dc587f3c244b/o1cn01tbad4q2frmoc48tst_2210971298933-0-cib-20230523081820-iel4w.png");
      }

      #BOX3027,
      #GROUP3026,
      #GROUP3025 {
        width: 420px;
        height: 464px;
      }

      #BOX3027>.ladi-box {
        border-width: 5px;
        border-radius: 20px;
        border-style: double;
        border-color: rgb(75, 75, 75);
        box-shadow: rgb(0, 0, 0) 0px 15px 20px -15px;
      }

      #COUNTDOWN3029 {
        width: 221.719px;
        height: 35.4145px;
        top: 20.9792px;
        left: 0px;
      }

      #COUNTDOWN3029>.ladi-countdown {
        font-size: 25px;
        font-weight: bold;
        color: rgb(74, 47, 47);
        text-align: center;
      }

      #COUNTDOWN3029>.ladi-countdown>.ladi-element {
        width: calc((100% - 30px) / 4);
        height: 100%;
        margin-right: 10px;
      }

      #COUNTDOWN3029>.ladi-countdown .ladi-countdown-background {
        background-color: rgb(209, 190, 190);
      }

      #HEADLINE3034 {
        width: 218px;
        top: 0px;
        left: 2.79px;
      }

      #HEADLINE3034>.ladi-headline {
        font-size: 14px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: center;
      }

      #HEADLINE3035 {
        width: 49px;
        top: 57.9334px;
        left: 6.86px;
      }

      #HEADLINE3035>.ladi-headline,
      #HEADLINE3037>.ladi-headline,
      #HEADLINE3038>.ladi-headline {
        font-size: 12px;
        line-height: 1.6;
        color: rgb(0, 0, 0);
      }

      #HEADLINE3036,
      #HEADLINE3037,
      #HEADLINE3038 {
        width: 41px;
      }

      #HEADLINE3036 {
        top: 57.9334px;
        left: 69.86px;
      }

      #HEADLINE3036>.ladi-headline {
        font-size: 12px;
        line-height: 1.6;
        color: rgb(24, 22, 33);
      }

      #HEADLINE3037 {
        top: 58.7033px;
        left: 125.86px;
      }

      #HEADLINE3038 {
        top: 57.9334px;
        left: 185.72px;
      }

      #GROUP3028 {
        width: 226.72px;
        height: 77.2447px;
        top: 59.2164px;
        left: 93.14px;
      }

      #FORM3039 {
        width: 335.5px;
        height: 292.072px;
        top: 149.345px;
        left: 42.75px;
      }

      #FORM3039>.ladi-form {
        font-size: 14px;
        line-height: 1.6;
        color: rgb(0, 0, 0);
      }

      #FORM3039 .ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item span[data-checked="false"],
      #FORM3039 .ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item .ladi-editing,
      #FORM3039 .ladi-form .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item .ladi-editing::placeholder,
      #FORM3039 .ladi-form .ladi-survey-option .ladi-survey-option-label,
      #FORM3039 .ladi-form-item .ladi-form-control::placeholder,
      #FORM3039 .ladi-form-item select.ladi-form-control[data-selected=""] {
        color: rgb(0, 0, 0);
      }

      #FORM3039 .ladi-form-item {
        padding-left: 8px;
        padding-right: 8px;
      }

      #FORM3039 .ladi-form-item.otp-countdown:before {
        right: 13px;
      }

      #FORM3039 .ladi-form-item.ladi-form-checkbox {
        padding-left: 13px;
        padding-right: 13px;
      }

      #FORM3039 .ladi-survey-option {
        text-align: left;
      }

      #FORM3039 .ladi-form-item-container,
      #FORM3039 .ladi-form-label-container .ladi-form-label-item {
        border-width: 1px;
        border-radius: 15px;
        border-color: rgb(238, 238, 238);
      }

      #FORM3039 .ladi-form-item-container .ladi-form-item.ladi-form-quantity {
        width: calc(100% + 1px);
      }

      #FORM3039 .ladi-form-item-container .ladi-form-quantity .button {
        background-color: rgb(238, 238, 238);
      }

      #FORM3039 .ladi-form-item-background {
        border-radius: 14px;
        background-image: linear-gradient(rgb(241, 243, 244), rgb(192, 192, 192));
        background-color: initial;
        background-size: initial;
        background-origin: initial;
        background-position: initial;
        background-repeat: initial;
        background-attachment: initial;
      }

      #BUTTON3040 {
        width: 229.957px;
        height: 39.312px;
        top: 252.76px;
        left: 52.5215px;
      }

      #BUTTON_TEXT3040 {
        width: 230px;
        top: 12.3513px;
        left: 0px;
      }

      #BUTTON_TEXT3040>.ladi-headline {
        font-size: 16px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #FORM_ITEM3042,
      #FORM_ITEM3043 {
        width: 335px;
        height: 50.0465px;
      }

      #FORM_ITEM3042 {
        top: 0px;
        left: 0.5px;
      }

      #FORM_ITEM3043 {
        top: 59.9379px;
        left: 0px;
      }

      #FORM_ITEM3044 {
        width: 335px;
        height: 60px;
        top: 176.753px;
        left: 0.5px;
      }

      #FORM_ITEM3044 .ladi-form-checkbox-item {
        margin: 5px;
      }

      #FORM_ITEM3044 .ladi-form-item.ladi-form-checkbox {
        padding-top: 5px;
        padding-bottom: 5px;
      }

      #FORM_ITEM3044 .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item input {
        width: 13px;
        height: 13px;
      }

      #FORM_ITEM3044 .ladi-form-item.ladi-form-checkbox .ladi-form-checkbox-item span {
        width: calc((100% - 5px) - 13px + 1px);
      }

      #FORM_ITEM3045 {
        width: 335px;
        height: 49.0706px;
        top: 119.876px;
        left: 0px;
      }

      #HEADLINE3046 {
        width: 313px;
        top: 20.7867px;
        left: 54px;
      }

      #HEADLINE3046>.ladi-headline {
        font-size: 26px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(255, 188, 1);
        text-align: center;
      }

      #PARAGRAPH3047 {
        width: 288px;
        top: 43.8831px;
        left: 66px;
      }

      #PARAGRAPH3047>.ladi-paragraph {
        font-size: 14px;
        line-height: 1.6;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #GROUP3026.ladi-animation>.ladi-group {
        animation-name: fadeInLeft;
        animation-delay: 0.2s;
        animation-duration: 1.5s;
        animation-iteration-count: 1;
      }

      #HEADLINE3048 {
        width: 220px;
        top: 9.51618px;
        left: 100px;
      }

      #HEADLINE3048>.ladi-headline {
        font-family: Roboto, sans-serif;
        font-size: 30px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(199, 31, 22);
        text-decoration-line: underline;
      }

      #GROUP3025 {
        top: 622.094px;
        left: 0px;
      }

      #HEADLINE3484 {
        width: 305px;
        top: 66.256px;
        left: 13.5px;
      }

      #HEADLINE3484>.ladi-headline {
        font-size: 14px;
        font-style: italic;
        line-height: 1.2;
        color: rgb(255, 188, 1);
        text-decoration-line: underline;
        text-align: center;
      }

      #HEADLINE3487 {
        width: 222px;
        top: 1px;
        left: 0px;
      }

      #HEADLINE3487>.ladi-headline {
        font-size: 16px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: center;
      }

      #HEADLINE3488 {
        width: 111px;
        top: 0px;
        left: 171px;
      }

      #HEADLINE3488>.ladi-headline {
        font-size: 17px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-decoration-line: line-through;
        text-align: left;
      }

      #GROUP3486 {
        width: 282px;
        height: 27px;
        top: 40px;
        left: 11.071px;
      }

      #BOX3490,
      #GROUP3489 {
        width: 332px;
        height: 41px;
      }

      #BOX3490>.ladi-box {
        background-image: url("https://w.ladicdn.com/s650x350/5c7362c6c417ab07e5196b05/ggggggggg-20200923023825.jpg");
        background-size: cover;
        background-origin: content-box;
        background-position: 50% 0%;
        background-repeat: repeat;
        background-attachment: scroll;
      }

      #HEADLINE3491 {
        width: 277px;
        top: 3px;
        left: 26.5px;
      }

      #HEADLINE3491>.ladi-headline {
        font-size: 21px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(0, 0, 0);
        text-align: center;
      }

      #GROUP3485 {
        width: 332px;
        height: 67px;
      }

      #GROUP3483 {
        width: 332px;
        height: 83.256px;
        top: 98.856px;
        left: 39.3477px;
      }

      #LIST_PARAGRAPH3530>.ladi-list-paragraph {
        font-size: 15px;
        font-weight: bold;
        line-height: 1.6;
        color: rgb(199, 31, 22);
      }

      #LIST_PARAGRAPH3530 ul li:before {
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22100%25%22%20height%3D%22100%25%22%20%20viewBox%3D%220%200%201792%201896.0833%22%20class%3D%22%22%20fill%3D%22rgba(10%2C%20103%2C%20233%2C%201)%22%3E%20%3Cpath%20d%3D%22M1671%20566q0%2040-28%2068l-724%20724-136%20136q-28%2028-68%2028t-68-28l-136-136-362-362q-28-28-28-68t28-68l136-136q28-28%2068-28t68%2028l294%20295%20656-657q28-28%2068-28t68%2028l136%20136q28%2028%2028%2068z%22%3E%3C%2Fpath%3E%20%3C%2Fsvg%3E");
      }

      #SECTION216 {
        height: 348.304px;
      }

      #SECTION216>.ladi-overlay {
        opacity: 0.4;
      }

      #SECTION216>.ladi-section-background {
        background-color: rgb(82, 82, 82);
      }

      #HEADLINE220 {
        width: 384px;
        top: 0px;
        left: 13px;
      }

      #HEADLINE220>.ladi-headline {
        font-size: 23px;
        font-weight: bold;
        line-height: 1.4;
        color: rgb(224, 224, 224);
        text-align: center;
      }

      #SHAPE222,
      #SHAPE225,
      #SHAPE228 {
        width: 22.5469px;
        height: 22.5469px;
      }

      #SHAPE222 {
        top: 0.302px;
        left: 0px;
      }

      #HEADLINE223 {
        width: 292px;
        top: 0px;
        left: 33px;
      }

      #HEADLINE223>.ladi-headline,
      #HEADLINE226>.ladi-headline,
      #HEADLINE229>.ladi-headline,
      #HEADLINE232>.ladi-headline,
      #HEADLINE235>.ladi-headline,
      #HEADLINE238>.ladi-headline,
      #HEADLINE241>.ladi-headline {
        font-size: 14px;
        line-height: 1.4;
        color: rgb(255, 255, 255);
        text-align: left;
      }

      #GROUP221 {
        width: 325px;
        height: 38px;
        top: 70.484px;
        left: 0px;
      }

      #HEADLINE226 {
        width: 378px;
        top: 1.849px;
        left: 33px;
      }

      #GROUP224 {
        width: 411px;
        height: 22.5469px;
        top: 98.12px;
        left: 0.5px;
      }

      #HEADLINE229 {
        top: 4px;
        left: 33px;
      }

      #GROUP227 {
        width: 402px;
        height: 24px;
        top: 41px;
        left: 0.5px;
      }

      #GROUP219 {
        width: 411.5px;
        height: 120.667px;
        top: 10px;
        left: 8.75px;
      }

      #LINE217 {
        width: 388px;
        top: 113.637px;
        left: 6.295px;
      }

      #LINE217>.ladi-line>.ladi-line-container {
        border-top: 3px solid rgb(0, 0, 0);
        border-right: 3px solid rgb(0, 0, 0);
        border-bottom: 3px solid rgb(0, 0, 0);
        border-left: 0px !important;
      }

      #HEADLINE230 {
        width: 283px;
        top: 0px;
        left: 46px;
      }

      #HEADLINE230>.ladi-headline {
        font-size: 18px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #HEADLINE232 {
        width: 211px;
      }

      #HEADLINE232,
      #HEADLINE241 {
        top: 0px;
        left: 32.65px;
      }

      #SHAPE233 {
        width: 22.6801px;
        height: 22.6801px;
      }

      #GROUP231 {
        width: 243.65px;
        height: 22.6801px;
        top: 37.5px;
        left: 0px;
      }

      #HEADLINE235 {
        width: 151px;
        top: 0px;
        left: 31.65px;
      }

      #SHAPE236 {
        width: 18.4385px;
        height: 18.4658px;
      }

      #GROUP234 {
        width: 182.65px;
        height: 39px;
        top: 39.34px;
        left: 209.35px;
      }

      #HEADLINE238 {
        top: 3px;
        left: 35px;
      }

      #SHAPE239 {
        width: 21.3362px;
        height: 21.3362px;
      }

      #GROUP237 {
        width: 167px;
        height: 22px;
        top: 80.5px;
        left: 0px;
      }

      #HEADLINE241 {
        width: 113px;
      }

      #SHAPE242 {
        width: 22.2998px;
        height: 24.6135px;
      }

      #GROUP240 {
        width: 145.65px;
        height: 38px;
        top: 80.5px;
        left: 209.35px;
      }

      #GROUP856 {
        width: 394.295px;
        height: 132.637px;
        top: 143.667px;
        left: 12.8525px;
      }

      #IMAGE244,
      #IMAGE244>.ladi-image>.ladi-image-background {
        width: 35.5258px;
        height: 22.6523px;
      }

      #IMAGE244 {
        top: 9.805px;
        left: 60.688px;
      }

      #IMAGE244>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/5c7362c6c417ab07e5196b05/mastercard-20200311062250-20200312040745.svg");
      }

      #IMAGE245,
      #IMAGE245>.ladi-image>.ladi-image-background {
        width: 35.526px;
        height: 22.6523px;
      }

      #IMAGE245 {
        top: 9.805px;
        left: 18.532px;
      }

      #IMAGE245>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/5c7362c6c417ab07e5196b05/visa-20200311062250-20200312040726.svg");
      }

      #IMAGE246,
      #IMAGE246>.ladi-image>.ladi-image-background {
        width: 79.4545px;
        height: 30.4573px;
      }

      #IMAGE246 {
        top: 6px;
        left: 100.214px;
      }

      #IMAGE246>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/5c7362c6c417ab07e5196b05/bo-cong-thuong-20200311062345-20200312040731.svg");
      }

      #FRAME243 {
        width: 201px;
        height: 41.306px;
      }

      #HEADLINE247 {
        width: 391px;
        top: 50px;
        left: 0px;
      }

      #HEADLINE247>.ladi-headline {
        font-size: 12px;
        line-height: 1.2;
        color: rgb(0, 0, 0);
        text-align: center;
      }

      #GROUP857 {
        width: 391px;
        height: 62px;
        top: 286.304px;
        left: 14.5px;
      }

      #POPUP328 {
        width: 360px;
        height: 481px;
        right: 0px;
        bottom: 0px;
        margin: auto;
      }

      #POPUP328>.ladi-popup>.ladi-popup-background {
        background-image: url("https://w.ladicdn.com/s360x481/57b167c9ca57d39c18a1c57c/pexels-photo-123.jpg");
        background-size: cover;
        background-origin: content-box;
        background-position: 50% 0%;
        background-repeat: no-repeat;
        background-attachment: scroll;
      }

      #POPUP328 .popup-close {
        background-image: url("data:image/svg+xml;utf8, %3Csvg%20width%3D%2224%22%20height%3D%2224%22%20viewBox%3D%220%200%2024%2024%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%23fff%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M23.4144%202.00015L2.00015%2023.4144L0.585938%2022.0002L22.0002%200.585938L23.4144%202.00015Z%22%3E%3C%2Fpath%3E%3Cpath%20fill-rule%3D%22evenodd%22%20clip-rule%3D%22evenodd%22%20d%3D%22M2.00015%200.585938L23.4144%2022.0002L22.0002%2023.4144L0.585938%202.00015L2.00015%200.585938Z%22%3E%3C%2Fpath%3E%3C%2Fsvg%3E");
      }

      #IMAGE329,
      #IMAGE329>.ladi-image>.ladi-image-background {
        width: 282.443px;
        height: 279px;
      }

      #IMAGE329 {
        top: 24px;
        left: 38.6745px;
      }

      #IMAGE329>.ladi-image>.ladi-image-background {
        background-image: url("https://w.ladicdn.com/s600x600/5dbe4694ed94dc587f3c244b/z3897655841208_1e0a5a9a9d15561270ecb349d17c0d38-20221121043247-l4k3m.jpg");
      }

      #IMAGE329>.ladi-image {
        border-radius: 11px;
      }

      #BOX331,
      #GROUP330 {
        width: 298px;
        height: 44px;
      }

      #BOX331>.ladi-box {
        background-color: rgb(10, 103, 233);
      }

      #HEADLINE332 {
        width: 228px;
        top: 11px;
        left: 35.0313px;
      }

      #HEADLINE332>.ladi-headline {
        font-size: 19px;
        font-weight: bold;
        line-height: 1.2;
        color: rgb(255, 255, 255);
        text-align: center;
      }

      #GROUP330 {
        top: 315px;
        left: 31px;
      }

      #PARAGRAPH333 {
        width: 296px;
        top: 383px;
        left: 32px;
      }

      #PARAGRAPH333>.ladi-paragraph {
        font-size: 14px;
        line-height: 1.4;
        color: rgb(82, 82, 82);
        text-align: center;
      }
    </style>
    <style id="style_lazyload" type="text/css">
      .ladi-overlay,
      .ladi-box,
      .ladi-button-background,
      .ladi-collection-item:before,
      .ladi-countdown-background,
      .ladi-form-item-background,
      .ladi-form-label-container .ladi-form-label-item.image,
      .ladi-frame-background,
      .ladi-gallery-view-item,
      .ladi-gallery-control-item,
      .ladi-headline,
      .ladi-image-background,
      .ladi-image-compare,
      .ladi-list-paragraph ul li:before,
      .ladi-section-background,
      .ladi-survey-option-background,
      .ladi-survey-option-image,
      .ladi-tabs-background,
      .ladi-video-background,
      .ladi-banner,
      .ladi-spin-lucky-screen,
      .ladi-spin-lucky-start {
        background-image: none !important;
      }
    </style>
    <script>
      ! function(f, b, e, v, n, t, s) {
        if (f.fbq) return;
        n = f.fbq = function() {
          n.callMethod ? n.callMethod.apply(n, arguments) : n.queue.push(arguments)
        };
        if (!f._fbq) f._fbq = n;
        n.push = n;
        n.loaded = !0;
        n.version = '2.0';
        n.queue = [];
        t = b.createElement(e);
        t.async = !0;
        t.src = v;
        s = b.getElementsByTagName(e)[0];
        s.parentNode.insertBefore(t, s)
      }(window, document, 'script', 'https://connect.facebook.net/en_US/fbevents.js');
      fbq("init", "707403993923991");
      fbq("track", "PageView");
      fbq("track", "ViewContent");
    </script>
    <noscript>
      <img height="1" width="1" style="display:none;" src="https://www.facebook.com/tr?id=707403993923991&ev=PageView&noscript=1" />
    </noscript>
    <script>
      ! function(e, t, r, n, c) {
        if (!e.ztrq) {
          c = e.ztrq = function() {
            c.queue ? c.queue.push(arguments) : c.call(c, arguments)
          }, e._ztrk || (e._ztrk = c), c.queue = [];
          var u = t.createElement(r);
          u.async = !0, u.src = n;
          var a = t.getElementsByTagName(r)[0];
          a.parentNode.insertBefore(u, a)
        }
      }(window, document, "script", "https://s.zzcdn.me/ztr/ztracker.js?id=7056840457216708608");
      window.LadiPageZaloAds = {
        auto_tracking: true
      };
      ztrq("track", "ViewContent");
    </script>
    <script>
      ! function(w, d, t) {
        w.TiktokAnalyticsObject = t;
        var ttq = w[t] = w[t] || [];
        ttq.methods = ["page", "track", "identify", "instances", "debug", "on", "off", "once", "ready", "alias", "group", "enableCookie", "disableCookie"], ttq.setAndDefer = function(t, e) {
          t[e] = function() {
            t.push([e].concat(Array.prototype.slice.call(arguments, 0)))
          }
        };
        for (var i = 0; i < ttq.methods.length; i++) ttq.setAndDefer(ttq, ttq.methods[i]);
        ttq.instance = function(t) {
          for (var e = ttq._i[t] || [], n = 0; n < ttq.methods.length; n++) ttq.setAndDefer(e, ttq.methods[n]);
          return e
        }, ttq.load = function(e, n) {
          var i = "https://analytics.tiktok.com/i18n/pixel/events.js";
          ttq._i = ttq._i || {}, ttq._i[e] = [], ttq._i[e]._u = i, ttq._t = ttq._t || {}, ttq._t[e] = +new Date, ttq._o = ttq._o || {}, ttq._o[e] = n || {};
          var o = document.createElement("script");
          o.type = "text/javascript", o.async = !0, o.src = i + "?sdkid=" + e + "&lib=" + t;
          var a = document.getElementsByTagName("script")[0];
          a.parentNode.insertBefore(o, a)
        };
        ttq.load('CII4P8JC77U6M5M2J7I0');
        ttq.page();
      }(window, document, 'ttq');
    </script>
  </head>
  <body>
    <svg xmlns="http://www.w3.org/2000/svg" style="width: 0px; height: 0px; position: absolute; overflow: hidden; display: none;">
      <symbol id="shape_mnrnWxDoOK" viewBox="0 0 100 100">
        <polygon points="50.331,79.217 19.427,97.562 27.331,62.504 0.331,38.784 36.113,35.463 50.331,2.453 64.546,35.463 100.331,38.784   73.332,62.504 81.235,97.562 "></polygon>
      </symbol>
      <symbol id="shape_pUOuupwEhH" viewBox="0 0 1664 1896.0833">
        <path d="M1664 647q0 22-26 48l-363 354 86 500q1 7 1 20 0 21-10.5 35.5T1321 1619q-19 0-40-12l-449-236-449 236q-22 12-40 12-21 0-31.5-14.5T301 1569q0-6 2-20l86-500L25 695Q0 668 0 647q0-37 56-46l502-73L783 73q19-41 49-41t49 41l225 455 502 73q56 9 56 46z"></path>
      </symbol>
      <symbol id="shape_GAiIkrqnGp" viewBox="0 0 1792 1896.0833">
        <path d="M45 1651q-19 19-32 13t-13-32V160q0-26 13-32t32 13l710 710q8 8 13 19V160q0-26 13-32t32 13l710 710q8 8 13 19V192q0-26 19-45t45-19h128q26 0 45 19t19 45v1408q0 26-19 45t-45 19h-128q-26 0-45-19t-19-45V922q-5 10-13 19l-710 710q-19 19-32 13t-13-32V922q-5 10-13 19z"></path>
      </symbol>
    </svg>
    <div class="ladi-wraper">
      <div id="BODY_BACKGROUND" class='ladi-section'>
        <div class='ladi-section-background'></div>
      </div>
      <div id="SECTION3293" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div data-action="true" id="HEADLINE3294" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>TRANG CHỦ</h3>
          </div>
          <div id="LINE3295" class='ladi-element'>
            <div class='ladi-line'>
              <div class="ladi-line-container"></div>
            </div>
          </div>
          <div id="SHAPE3296" class='ladi-element'>
            <div class='ladi-shape ladi-transition'>
              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 24 24" fill="rgba(0, 0, 0, 1)">
                <path d="M3,6H21V8H3V6M3,11H21V13H3V11M3,16H21V18H3V16Z"></path>
              </svg>
            </div>
          </div>
          <div data-action="true" id="BUTTON3297" class='ladi-element'>
            <div class='ladi-button ladi-transition'>
              <div class="ladi-button-background"></div>
              <div id="BUTTON_TEXT3297" class='ladi-element ladi-button-headline'>
                <p class='ladi-headline ladi-transition'>&nbsp; &nbsp; ĐẶT HÀNG</p>
              </div>
            </div>
          </div>
          <div id="SHAPE3299" class='ladi-element'>
            <div class='ladi-shape ladi-transition'>
              <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 33 33" enable-background="new 0 0 33 33" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 255, 255, 1.0)">
                <path d="M26.905,10.036c-3.09,0-5.595,2.505-5.595,5.596s2.505,5.595,5.595,5.595c3.091,0,5.595-2.504,5.595-5.595  S29.996,10.036,26.905,10.036z M30.511,16.487h-2.75v2.749H26.05v-2.749h-2.751v-1.712h2.751v-2.75h1.711v2.75h2.75V16.487z"></path>
                <path d="M27.069,22.339c-0.101,0.52-0.201,1.027-0.298,1.505h-3.577l0.457-2.339c-0.383-0.213-0.742-0.462-1.074-0.742l-0.602,3.081  h-5.102v-3.796h4.979c-0.32-0.367-0.605-0.766-0.842-1.195h-4.138v-3.374h3.323c0.024-1.081,0.301-2.099,0.777-2.996H5.83V8.058  c0-1.979,1.609-3.588,3.588-3.588h1.899c0.17,0.545,0.674,0.943,1.275,0.943h7.366c0.603,0,1.106-0.398,1.275-0.943h1.901  c1.978,0,3.587,1.609,3.587,3.588v0.866c0.062-0.002,0.121-0.009,0.183-0.009c0.346,0,0.683,0.033,1.014,0.084V8.058  c0-2.638-2.146-4.783-4.783-4.783h-2.104c-0.244-0.328-0.631-0.543-1.072-0.543h-7.366c-0.441,0-0.827,0.215-1.071,0.543H9.418  c-2.638,0-4.784,2.146-4.784,4.783v4.425H1.998c-0.826,0-1.498,0.671-1.498,1.497c0,0.828,0.672,1.499,1.498,1.499h0.394  c0.417,2.352,1.697,9.487,2.293,11.691c0.71,2.63,1.534,3.098,3.5,3.098l15.604-0.002l0.58,0.002c1.965,0,2.789-0.468,3.498-3.098  c0.271-1.001,0.682-3.019,1.09-5.145C28.36,22.217,27.727,22.324,27.069,22.339z M5.232,13.263c0.396,0,0.718,0.322,0.718,0.717  c0,0.397-0.321,0.719-0.718,0.719c-0.396,0-0.719-0.321-0.719-0.719C4.514,13.585,4.837,13.263,5.232,13.263z M4.214,15.479h3.512  l0.658,3.374H4.825C4.578,17.509,4.36,16.294,4.214,15.479z M5.781,23.844c-0.237-1.169-0.493-2.502-0.734-3.796h3.57l0.742,3.796  H5.781z M8.765,28.472l-0.58,0.002c-1.192,0-1.289,0-1.769-1.771c-0.107-0.396-0.239-0.977-0.387-1.662h3.563l0.671,3.432H8.765z   M15.679,28.473l-4.197-0.001l-0.67-3.432h4.867V28.473z M15.679,23.844h-5.102l-0.74-3.796h5.842V23.844z M15.679,18.853H9.602  l-0.657-3.374h6.734V18.853z M21.071,28.474l-4.197-0.001v-3.433h4.867L21.071,28.474z M26.136,26.703  c-0.477,1.768-0.574,1.771-1.761,1.771c-0.002,0-0.004,0-0.006,0H22.29l0.671-3.434h3.563  C26.377,25.726,26.242,26.306,26.136,26.703z"></path>
              </svg>
            </div>
          </div>
          <div data-action="true" id="HEADLINE3300" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>&nbsp;SẢN PHẨM <br>
            </h3>
          </div>
          <div id="LINE3301" class='ladi-element'>
            <div class='ladi-line'>
              <div class="ladi-line-container"></div>
            </div>
          </div>
          <div data-action="true" id="HEADLINE3302" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>ĐÁNH GIÁ</h3>
          </div>
          <div id="LINE3303" class='ladi-element'>
            <div class='ladi-line'>
              <div class="ladi-line-container"></div>
            </div>
          </div>
          <div data-action="true" id="HEADLINE3304" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>LIÊN HỆ</h3>
          </div>
        </div>
      </div>
      <div id="SECTION2590" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GALLERY3549" class='ladi-element'>
            <div class='ladi-gallery ladi-gallery-bottom'>
              <div class="ladi-gallery-view">
                <div class="ladi-gallery-view-arrow ladi-gallery-view-arrow-left opacity-0"></div>
                <div class="ladi-gallery-view-arrow ladi-gallery-view-arrow-right opacity-0"></div>
                <div class="ladi-gallery-view-item selected" data-index="0"></div>
                <div class="ladi-gallery-view-item" data-index="1"></div>
                <div class="ladi-gallery-view-item" data-index="2"></div>
                <div class="ladi-gallery-view-item" data-index="3"></div>
                <div class="ladi-gallery-view-item" data-index="4"></div>
                <div class="ladi-gallery-view-item" data-index="5"></div>
                <div class="ladi-gallery-view-item" data-index="6"></div>
                <div class="ladi-gallery-view-item" data-index="7"></div>
                <div class="ladi-gallery-view-item" data-index="8"></div>
                <div class="ladi-gallery-view-item" data-index="9"></div>
              </div>
              <div class="ladi-gallery-control">
                <div class="ladi-gallery-control-box">
                  <div class="ladi-gallery-control-item selected" data-index="0"></div>
                  <div class="ladi-gallery-control-item" data-index="1"></div>
                  <div class="ladi-gallery-control-item" data-index="2"></div>
                  <div class="ladi-gallery-control-item" data-index="3"></div>
                  <div class="ladi-gallery-control-item" data-index="4"></div>
                  <div class="ladi-gallery-control-item" data-index="5"></div>
                  <div class="ladi-gallery-control-item" data-index="6"></div>
                  <div class="ladi-gallery-control-item" data-index="7"></div>
                  <div class="ladi-gallery-control-item" data-index="8"></div>
                  <div class="ladi-gallery-control-item" data-index="9"></div>
                </div>
                <div class="ladi-gallery-control-arrow ladi-gallery-control-arrow-left opacity-0"></div>
                <div class="ladi-gallery-control-arrow ladi-gallery-control-arrow-right opacity-0"></div>
              </div>
            </div>
          </div>
          <div id="GROUP3307" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE3308" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>Tư vấn miễn phí online: 0982.872.123</h3>
              </div>
              <div id="GROUP3309" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="HEADLINE3310" class='ladi-element'>
                    <h3 class='ladi-headline ladi-transition'>2474</h3>
                  </div>
                  <div id="HEADLINE3311" class='ladi-element'>
                    <h3 class='ladi-headline ladi-transition'>Đã bán</h3>
                  </div>
                  <div id="HEADLINE3312" class='ladi-element'>
                    <h3 class='ladi-headline ladi-transition'>4.9</h3>
                  </div>
                  <div id="GROUP3313" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="SHAPE3314" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 188, 1, 1)">
                            <use xlink:href="#shape_mnrnWxDoOK"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3315" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 188, 1, 1)">
                            <use xlink:href="#shape_mnrnWxDoOK"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3316" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 188, 1, 1)">
                            <use xlink:href="#shape_mnrnWxDoOK"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3317" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 188, 1, 1)">
                            <use xlink:href="#shape_mnrnWxDoOK"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3318" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" style="enable-background:new 0 0 100 100;" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 188, 1, 1)">
                            <use xlink:href="#shape_mnrnWxDoOK"></use>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div id="GROUP3319" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="LIST_PARAGRAPH3320" class='ladi-element'>
                    <div class='ladi-list-paragraph ladi-transition'>
                      <ul>
                        <li>Sale 45%</li>
                      </ul>
                    </div>
                  </div>
                  <div id="LIST_PARAGRAPH3321" class='ladi-element'>
                    <div class='ladi-list-paragraph ladi-transition'>
                      <ul>
                        <li>Freeship</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
              <div id="GROUP3322" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="GROUP3323" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="BOX3324" class='ladi-element'>
                        <div class='ladi-box ladi-transition'></div>
                      </div>
                      <div data-action="true" id="BUTTON3325" class='ladi-element'>
                        <div class='ladi-button ladi-transition'>
                          <div class="ladi-button-background"></div>
                          <div id="BUTTON_TEXT3325" class='ladi-element ladi-button-headline'>
                            <p class='ladi-headline ladi-transition'>
                              <span class="ladipage-animated-headline clip">
                                <span class="ladipage-animated-words-wrapper" data-word="[&quot;Freeship&quot;]">Đặt mua ngay</span>
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                      <div id="GROUP3327" class='ladi-element'>
                        <div class='ladi-group'>
                          <div id="HEADLINE3328" class='ladi-element'>
                            <h3 class='ladi-headline ladi-transition'>1.390.000Đ</h3>
                          </div>
                          <div id="HEADLINE3329" class='ladi-element'>
                            <h3 class='ladi-headline ladi-transition'>Giá chỉ</h3>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="HEADLINE3330" class='ladi-element'>
                    <h3 class='ladi-headline ladi-transition'>2.400.000Đ</h3>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="HEADLINE2539" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>MÁY HÀN ĐIỆN CẦM TAY</h3>
          </div>
          <div id="IMAGE3334" class='ladi-element'>
            <div class='ladi-image ladi-transition'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3335" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3686" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3683" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3342" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>THÔNG TIN SẢN PHẨM</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3709" class='ladi-element'>
            <div class='ladi-image ladi-transition'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="IMAGE3746" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3727" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3707" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="LIST_PARAGRAPH3684" class='ladi-element'>
                <div class='ladi-list-paragraph ladi-transition'>
                  <ul>
                    <li>Phục vụ tận tâm, hỗ trợ khách hàng 24/7</li>
                    <li>Đặt chữ TÂM lên đầu trong kinh doanh</li>
                    <li>Khuyến khích kiểm tra hàng trước khi thanh toán&nbsp;</li>
                  </ul>
                </div>
              </div>
              <div id="IMAGE3758" class='ladi-element'>
                <div class='ladi-image ladi-transition'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
            </div>
          </div>
          <div id="BOX3711" class='ladi-element'>
            <div class='ladi-box ladi-transition'></div>
          </div>
          <div id="HEADLINE3735" class='ladi-element'>
            <h3 class='ladi-headline'>0 - 200A</h3>
          </div>
        </div>
      </div>
      <div id="SECTION3683" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3687" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3684" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3687" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>CHẤT LƯỢNG MỐI HÀN</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3721" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3691" class='ladi-element'>
            <div class='ladi-group'>
              <div id="SHAPE3687" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1792 1896.08" class="" fill="rgba(255, 188, 1, 1)">
                    <use xlink:href="#shape_GAiIkrqnGp"></use>
                  </svg>
                </div>
              </div>
              <div id="HEADLINE3693" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>Chất lượng mối hàn: <span style="background-color: rgb(191, 2, 2);">BÓNG ĐẸP, SẮC NÉT</span>
                </h3>
              </div>
            </div>
          </div>
          <div id="IMAGE3722" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3703" class='ladi-element'>
            <div class='ladi-group'>
              <div id="SHAPE3691" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1792 1896.08" class="" fill="rgba(255, 188, 1, 1)">
                    <use xlink:href="#shape_GAiIkrqnGp"></use>
                  </svg>
                </div>
              </div>
              <div id="HEADLINE3710" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>Có dòng điện ổn định hàn cả ngày không nóng máy, từ 3li2 trở xuống.&nbsp;</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3691" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3704" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3698" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3711" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>THIẾT KẾ THÔNG MINH</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3725" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="IMAGE3748" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3699" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="LIST_PARAGRAPH3685" class='ladi-element'>
            <div class='ladi-list-paragraph'>
              <ul>
                <li>Chỉ cần cắm điện, lắp que hàn, điều chỉnh dòng điện là bạn đã có thể sử dụng&nbsp;</li>
                <li>Dễ dàng mang theo tiện lợi gọn nhẹ mà không cần quá nhiều các phụ kiện cồng kềnh</li>
              </ul>
            </div>
          </div>
          <div id="HEADLINE3731" class='ladi-element'>
            <h3 class='ladi-headline'>KHÁC BIỆT MÁY HÀN CŨ</h3>
          </div>
          <div id="VIDEO3683" class='ladi-element'>
            <div class='ladi-video'>
              <div class="ladi-video-background"></div>
              <div id="SHAPE3683" class='ladi-element'>
                <div class='ladi-shape'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 408.7 408.7" fill="rgba(0, 0, 0, 0.5)">
                    <polygon fill="#fff" points="163.5 296.3 286.1 204.3 163.5 112.4 163.5 296.3"></polygon>
                    <path d="M204.3,0C91.5,0,0,91.5,0,204.3S91.5,408.7,204.3,408.7s204.3-91.5,204.3-204.3S316.7,0,204.3,0ZM163.5,296.3V112.4l122.6,91.9Z" transform="translate(0 0)"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div id="IMAGE3760" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3731" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE3732" class='ladi-element'>
                <h3 class='ladi-headline'>MÁY HÀN CŨ CỒNG KỀNH BẤT TIỆN</h3>
              </div>
              <div id="SHAPE3698" class='ladi-element'>
                <div class='ladi-shape'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1408 1896.0833" class="" fill="rgba(191, 2, 2, 1)">
                    <path d="M1298 1322q0 40-28 68l-136 136q-28 28-68 28t-68-28l-294-294-294 294q-28 28-68 28t-68-28l-136-136q-28-28-28-68t28-68l294-294-294-294q-28-28-28-68t28-68l136-136q28-28 68-28t68 28l294 294 294-294q28-28 68-28t68 28l136 136q28 28 28 68t-28 68L976 960l294 294q28 28 28 68z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div id="IMAGE3761" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="BOX3709" class='ladi-element'>
            <div class='ladi-box ladi-transition'></div>
          </div>
          <div id="GROUP3732" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE3733" class='ladi-element'>
                <h3 class='ladi-headline'>MÁY HÀN MỚI CẦM TAY TIỆN GỌN</h3>
              </div>
              <div id="SHAPE3699" class='ladi-element'>
                <div class='ladi-shape'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1792 1896.0833" class="" fill="rgba(3, 255, 111, 1)">
                    <path d="M1671 566q0 40-28 68l-724 724-136 136q-28 28-68 28t-68-28l-136-136-362-362q-28-28-28-68t28-68l136-136q28-28 68-28t68 28l294 295 656-657q28-28 68-28t68 28l136 136q28 28 28 68z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3692" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3707" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3699" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3714" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>CẤU TẠO SẢN PHẨM</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3727" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="IMAGE3757" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="HEADLINE3729" class='ladi-element'>
            <h3 class='ladi-headline'>cường độ dòng điện</h3>
          </div>
        </div>
      </div>
      <div id="SECTION3693" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3708" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3700" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3715" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>COMBO VÀ LƯU Ý SỬ DỤNG</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3728" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3733" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3710" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3734" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>COMBO SẢN PHẨM</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3762" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="IMAGE3763" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="IMAGE3765" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3694" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3709" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3701" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3716" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>CÔNG NGHỆ CẢI TIẾN VƯỢT TRỘI</h2>
              </div>
            </div>
          </div>
          <div id="GROUP3710" class='ladi-element'>
            <div class='ladi-group'>
              <div id="IMAGE3729" class='ladi-element'>
                <div class='ladi-image'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
              <div id="HEADLINE3717" class='ladi-element'>
                <h3 class='ladi-headline'>
                  <span style="background-color: rgb(255, 255, 255);">CHIPSET CAO CẤP 2023</span>
                </h3>
              </div>
            </div>
          </div>
          <div id="IMAGE3731" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3696" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3716" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3703" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3722" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>HÌNH ẢNH MỐI HÀN THỰC TẾ</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3747" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3722" class='ladi-element'>
            <div class='ladi-group'>
              <div id="GROUP3719" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="IMAGE3738" class='ladi-element'>
                    <div class='ladi-image'>
                      <div class="ladi-image-background"></div>
                    </div>
                  </div>
                  <div id="IMAGE3741" class='ladi-element'>
                    <div class='ladi-image'>
                      <div class="ladi-image-background"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div id="IMAGE3749" class='ladi-element'>
                <div class='ladi-image'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3697" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="GROUP3718" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX3704" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3724" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>ỨNG DỤNG ĐA DẠNG</h2>
              </div>
            </div>
          </div>
          <div id="IMAGE3745" class='ladi-element'>
            <div class='ladi-image'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION2776" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="HEADLINE2777" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'></h3>
          </div>
          <div id="GROUP2783" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX2784" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE2785" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>1 đổi 1 trong 7 ngày nếu phát hiện ra lỗi do sản phẩm !</h3>
              </div>
              <div id="HEADLINE2786" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>đổi trả</h3>
              </div>
              <div id="BOX2787" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="BOX2788" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="SHAPE2789" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns:dc="http://purl.org/dc/elements/1.1/" xmlns:cc="http://creativecommons.org/ns#" xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#" xmlns:svg="http://www.w3.org/2000/svg" xmlns="http://www.w3.org/2000/svg" xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd" xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(255, 255, 255, 1.0)">
                    <g transform="translate(0,-952.36218)">
                      <path style="text-indent:0;text-transform:none;direction:ltr;block-progression:tb;baseline-shift:baseline;color:#000000;enable-background:accumulate;" d="m 39.343752,964.25286 -10.6875,5.65625 31.71875,17.21875 10.374996,-6 -5.6875,-3.03125 a 1.0001,1.0001 0 0 1 -0.0937,-0.0312 l -0.0625,-0.0312 -25.562496,-13.78125 z m -12.78125,6.78125 -7.1875,3.8125 31.5625,17.75 7.4375,-4.28125 -31.8125,-17.28125 z m -9.28125,4.9375 -9.8125,5.1875 31.625,18.3125 9.875,-5.71875 -31.59375,-17.75 a 1.0001,1.0001 0 0 1 -0.03125,0 l -0.0625,-0.0312 z m -10.875,6.90625 0,36.65614 31.6875,18.3438 0,-36.6562 -31.6875,-18.34374 z m 65.374996,0 -10.468746,6.0625 -0.03125,7.71875 a 1.0001,1.0001 0 0 1 -0.5,0.84375 l -9.53125,5.59364 a 1.0001,1.0001 0 0 1 -1.53125,-0.8437 l -0.1875,-6.53119 -9.4375,5.46869 0,36.6875 23.156246,-13.4063 0,-26.5624 a 1.0001,1.0001 0 0 1 0.0937,-0.4375 1.0001,1.0001 0 0 1 0.0625,-0.0937 1.0001,1.0001 0 0 1 0.0625,-0.0625 1.0001,1.0001 0 0 1 0.0625,-0.0937 1.0001,1.0001 0 0 1 0.0625,-0.0625 1.0001,1.0001 0 0 1 0.1875,-0.125 1.0001,1.0001 0 0 1 0.46875,-0.125 l 7.53125,0 0,-14.03125 z m -12.468746,7.21875 -7.78125,4.5 0.15625,5.93749 7.59375,-4.46874 0.03125,-5.96875 z m 5.937496,8.8125 0,26.15619 a 1.0001,1.0001 0 0 1 0,0.125 l 0,15.2812 28.34375,0 0,-10.5937 a 1.0001,1.0001 0 0 1 0,-0.094 l 0,-0.062 0,-30.81239 -20.8125,0 -7.53125,0 z m 4.5,5.43739 a 1.001098,1.001098 0 0 1 0.0937,0 1.0001,1.0001 0 0 1 0.0937,0 l 12.8125,0 a 1.0001,1.0001 0 1 1 0,2 l -12.8125,0 a 1.0043849,1.0043849 0 0 1 -0.1875,-2 z m -0.0937,6.6563 a 1.0043849,1.0043849 0 0 1 0.0937,0 1.0001,1.0001 0 0 1 0.1875,0 l 17.78125,0 a 1.0001,1.0001 0 1 1 0,2 l -17.78125,0 a 1.0098393,1.0098393 0 0 1 -0.28125,-2 z m 0,6.625 a 1.0043849,1.0043849 0 0 1 0.0937,0 1.0001,1.0001 0 0 1 0.1875,0 l 17.78125,0 a 1.0001,1.0001 0 1 1 0,2 l -17.78125,0 a 1.0098393,1.0098393 0 0 1 -0.28125,-2 z m 22.46875,6.75 a 1.0001,1.0001 0 0 1 0.8125,1.75 l -7.59375,6.6875 a 1.0001,1.0001 0 0 1 -1.4375,-0.125 l -2.25,-2.8438 a 1.0004882,1.0004882 0 1 1 1.5625,-1.25 l 1.59375,2.0313 6.8125,-6 a 1.0001,1.0001 0 0 1 0.5,-0.25 z m -13.28125,0.625 a 1.0001,1.0001 0 0 1 0.0937,0 1.0001,1.0001 0 0 1 0.0937,0 l 7.0625,0 a 1.0001,1.0001 0 1 1 0,2 l -6.0625,0 0,7.5625 8.46875,0 0,-2 a 1.0001,1.0001 0 1 1 2,0 l 0,3 a 1.0001,1.0001 0 0 1 -1,1 l -10.46875,0 a 1.0001,1.0001 0 0 1 -1,-1 l 0,-9.5625 a 1.0001,1.0001 0 0 1 0.8125,-1 z" fill-opacity="1" stroke="none" marker="none" visibility="visible" display="inline" overflow="visible"></path>
                    </g>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP2790" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX2791" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE2792" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>Giao hàng nhanh chóng tận tay khách hàng trên toàn quốc&nbsp;</h3>
              </div>
              <div id="HEADLINE2793" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>vận chuyển</h3>
              </div>
              <div id="BOX2794" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="BOX2795" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="SHAPE2796" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 24 24" class="" fill="rgba(255, 255, 255, 1.0)">
                    <path d="M18,18.5A1.5,1.5 0 0,0 19.5,17A1.5,1.5 0 0,0 18,15.5A1.5,1.5 0 0,0 16.5,17A1.5,1.5 0 0,0 18,18.5M19.5,9.5H17V12H21.46L19.5,9.5M6,18.5A1.5,1.5 0 0,0 7.5,17A1.5,1.5 0 0,0 6,15.5A1.5,1.5 0 0,0 4.5,17A1.5,1.5 0 0,0 6,18.5M20,8L23,12V17H21A3,3 0 0,1 18,20A3,3 0 0,1 15,17H9A3,3 0 0,1 6,20A3,3 0 0,1 3,17H1V6C1,4.89 1.89,4 3,4H17V8H20M8,6V9H5V11H8V14H10V11H13V9H10V6H8Z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP2797" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX2798" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE2799" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>Chỉ thanh toán sau khi nhận được hàng và đồng ý mua hàng</h3>
              </div>
              <div id="HEADLINE2800" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>THANH TOÁN</h3>
              </div>
              <div id="BOX2801" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="BOX2802" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="SHAPE2803" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1920 1896.0833" class="" fill="rgba(255, 255, 255, 1.0)">
                    <path d="M1760 128q66 0 113 47t47 113v1216q0 66-47 113t-113 47H160q-66 0-113-47T0 1504V288q0-66 47-113t113-47h1600zM160 256q-13 0-22.5 9.5T128 288v224h1664V288q0-13-9.5-22.5T1760 256H160zm1600 1280q13 0 22.5-9.5t9.5-22.5V896H128v608q0 13 9.5 22.5t22.5 9.5h1600zM256 1408v-128h256v128H256zm384 0v-128h384v128H640z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP2804" class='ladi-element'>
            <div class='ladi-group'>
              <div id="BOX2805" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE2806" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>Mang đến khách hàng sp có ích và chất lượng luôn đảm bảo</h3>
              </div>
              <div id="HEADLINE2807" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>CHẤT LƯỢNG</h3>
              </div>
              <div id="BOX2808" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="BOX2809" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="SHAPE2810" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1408 1896.0833" class="" fill="rgba(255, 255, 255, 1.0)">
                    <path d="M384 1152v128H256v-128h128zm0-768v128H256V384h128zm768 0v128h-128V384h128zM128 1407h384v-383H128v383zm0-767h384V256H128v384zm768 0h384V256H896v384zM640 896v640H0V896h640zm512 512v128h-128v-128h128zm256 0v128h-128v-128h128zm0-512v384h-384v-128H896v384H768V896h384v128h128V896h128zM640 128v640H0V128h640zm768 0v640H768V128h640z"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP2780" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE2781" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>QUYỀN LỢI KHÁCH HÀNG</h2>
              </div>
              <div id="LINE2782" class='ladi-element'>
                <div class='ladi-line'>
                  <div class="ladi-line-container"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION1356" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="IMAGE1360" class='ladi-element'>
            <div class='ladi-image ladi-transition'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="HEADLINE1361" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>CHÚNG TÔI SẼ HẾT LÒNG HỖ TRỢ QUÝ KHÁCH</h3>
          </div>
          <div data-action="true" id="BUTTON1362" class='ladi-element'>
            <div class='ladi-button ladi-transition'>
              <div class="ladi-button-background"></div>
              <div id="BUTTON_TEXT1362" class='ladi-element ladi-button-headline'>
                <p class='ladi-headline ladi-transition'>0982872123</p>
              </div>
            </div>
          </div>
          <div id="HEADLINE1364" class='ladi-element'>
            <h3 class='ladi-headline ladi-transition'>Đội ngũ nhân viên chăm sóc khách hàng giàu kinh nghiệm của TMAX sẽ hộ trợ quý khách giải đáp mọi thắc mắc. "Lắng nghe" và "thấu hiểu' là phương châm của TMAX, với mong muốn đem đến những trải nghiệm tuyệt vời nhất cho quý khách cũng như những sản phẩm chất lượng nhât·. Trân trọng cảm ơn!</h3>
          </div>
          <div id="IMAGE1367" class='ladi-element'>
            <div class='ladi-image ladi-transition'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3585" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="LINE3586" class='ladi-element'>
            <div class='ladi-line'>
              <div class="ladi-line-container"></div>
            </div>
          </div>
          <div id="HEADLINE3587" class='ladi-element'>
            <h2 class='ladi-headline ladi-transition'>1736 Bình luận</h2>
          </div>
          <div id="HEADLINE3588" class='ladi-element'>
            <h2 class='ladi-headline ladi-transition'>Tất cả bình luận</h2>
          </div>
          <div id="GROUP3589" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE3590" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>Sắp xếp theo</h2>
              </div>
              <div id="SHAPE3591" class='ladi-element'>
                <div class='ladi-shape ladi-transition'>
                  <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1055.1139 1896.0833" fill="rgba(95, 95, 95, 1.0)">
                    <path d="M1024 1088q0 26-19 45l-448 448q-19 19-45 19t-45-19L19 1133q-19-19-19-45t19-45 45-19h896q26 0 45 19t19 45zm0-384q0 26-19 45t-45 19H64q-26 0-45-19T0 704t19-45l448-448q19-19 45-19t45 19l448 448q19 19 19 45z"></path>
                  </svg>
                </div>
              </div>
              <div id="BOX3592" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="HEADLINE3593" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>Hàng đầu</h2>
              </div>
            </div>
          </div>
          <div id="GROUP3594" class='ladi-element'>
            <div class='ladi-group'>
              <div id="GROUP3595" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="SHAPE3596" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                        <use xlink:href="#shape_pUOuupwEhH"></use>
                      </svg>
                    </div>
                  </div>
                  <div id="SHAPE3597" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                        <use xlink:href="#shape_pUOuupwEhH"></use>
                      </svg>
                    </div>
                  </div>
                  <div id="SHAPE3598" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                        <use xlink:href="#shape_pUOuupwEhH"></use>
                      </svg>
                    </div>
                  </div>
                  <div id="SHAPE3599" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                        <use xlink:href="#shape_pUOuupwEhH"></use>
                      </svg>
                    </div>
                  </div>
                  <div id="SHAPE3600" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                        <use xlink:href="#shape_pUOuupwEhH"></use>
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
              <div id="PARAGRAPH3601" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>Thích - Phản hồi</div>
              </div>
              <div id="IMAGE3602" class='ladi-element'>
                <div class='ladi-image ladi-transition'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
              <div id="PARAGRAPH3603" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>4</div>
              </div>
              <div id="PARAGRAPH3604" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>56 phút trước</div>
              </div>
            </div>
          </div>
          <div id="GROUP3605" class='ladi-element'>
            <div class='ladi-group'>
              <div id="PARAGRAPH3606" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>Mua về dùng trong gia đình tiện thật, hàn lại mấy cái khung cũng tiện, mối hàn đẹp, nói chung là oke 10 điểm nhé shop.</div>
              </div>
              <div id="PARAGRAPH3607" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>Lâm Vĩnh&nbsp;</div>
              </div>
              <div id="IMAGE3608" class='ladi-element'>
                <div class='ladi-image ladi-transition'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP3609" class='ladi-element'>
            <div class='ladi-group'>
              <a href="https://www.facebook.com/GDN-Gia%CC%80y-Da-Bo%CC%80-Th%C3%A2%CC%A3t-111572890318415/" target="_blank" id="BOX3610" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </a>
              <div id="PARAGRAPH3611" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>Cảm ơn anh anh đã tin tưởng sử dụng sản phẩm bên em! <br>
                </div>
              </div>
              <div id="PARAGRAPH3612" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>TMAXTECH</div>
              </div>
              <div id="PARAGRAPH3613" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>Phản hồi -</div>
              </div>
              <div id="PARAGRAPH3614" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>52 phút trước</div>
              </div>
            </div>
          </div>
          <div id="IMAGE3637" class='ladi-element'>
            <div class='ladi-image ladi-transition'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3684" class='ladi-element'>
            <div class='ladi-group'>
              <div id="GROUP3638" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="GROUP3639" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="IMAGE3640" class='ladi-element'>
                        <div class='ladi-image ladi-transition'>
                          <div class="ladi-image-background"></div>
                        </div>
                      </div>
                      <div id="PARAGRAPH3641" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>Bán thế nào đó ạ? Em muốn mua 1 bộ như video ạ?</div>
                      </div>
                      <div id="PARAGRAPH3642" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>Hoàng Công Thắng</div>
                      </div>
                    </div>
                  </div>
                  <div id="GROUP3643" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="PARAGRAPH3645" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>Dạ anh Thắng kiếm tra tin nhắn giúp shop ạ <br>
                        </div>
                      </div>
                      <div id="PARAGRAPH3646" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>TMAXTECH</div>
                      </div>
                      <div id="PARAGRAPH3647" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>Phản hồi -</div>
                      </div>
                      <div id="PARAGRAPH3648" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>47 phút trước</div>
                      </div>
                    </div>
                  </div>
                  <div id="GROUP3649" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="SHAPE3650" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                            <use xlink:href="#shape_pUOuupwEhH"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3651" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                            <use xlink:href="#shape_pUOuupwEhH"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3652" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                            <use xlink:href="#shape_pUOuupwEhH"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3653" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                            <use xlink:href="#shape_pUOuupwEhH"></use>
                          </svg>
                        </div>
                      </div>
                      <div id="SHAPE3654" class='ladi-element'>
                        <div class='ladi-shape ladi-transition'>
                          <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                            <use xlink:href="#shape_pUOuupwEhH"></use>
                          </svg>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div id="GROUP3655" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="PARAGRAPH3656" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>Thích - Phản hồi</div>
                      </div>
                      <div id="IMAGE3657" class='ladi-element'>
                        <div class='ladi-image ladi-transition'>
                          <div class="ladi-image-background"></div>
                        </div>
                      </div>
                      <div id="PARAGRAPH3658" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>2</div>
                      </div>
                      <div id="PARAGRAPH3659" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>40 phút trước</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <a href="https://www.facebook.com/GDN-Gia%CC%80y-Da-Bo%CC%80-Th%C3%A2%CC%A3t-111572890318415/" target="_blank" id="BOX3682" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </a>
            </div>
          </div>
          <div id="GROUP3702" class='ladi-element'>
            <div class='ladi-group'>
              <div id="GROUP3615" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="IMAGE3616" class='ladi-element'>
                    <div class='ladi-image ladi-transition'>
                      <div class="ladi-image-background"></div>
                    </div>
                  </div>
                  <div id="PARAGRAPH3617" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>Được cái không cồng kềnh, cắm điện cái là sử dụng được, cầm chắc tay, hàn ngon.</div>
                  </div>
                  <div id="PARAGRAPH3618" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>Hoàng Nam</div>
                  </div>
                </div>
              </div>
              <div id="GROUP3619" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="GROUP3620" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="GROUP3621" class='ladi-element'>
                        <div class='ladi-group'>
                          <div id="SHAPE3622" class='ladi-element'>
                            <div class='ladi-shape ladi-transition'>
                              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                                <use xlink:href="#shape_pUOuupwEhH"></use>
                              </svg>
                            </div>
                          </div>
                          <div id="SHAPE3623" class='ladi-element'>
                            <div class='ladi-shape ladi-transition'>
                              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                                <use xlink:href="#shape_pUOuupwEhH"></use>
                              </svg>
                            </div>
                          </div>
                          <div id="SHAPE3624" class='ladi-element'>
                            <div class='ladi-shape ladi-transition'>
                              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                                <use xlink:href="#shape_pUOuupwEhH"></use>
                              </svg>
                            </div>
                          </div>
                          <div id="SHAPE3625" class='ladi-element'>
                            <div class='ladi-shape ladi-transition'>
                              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                                <use xlink:href="#shape_pUOuupwEhH"></use>
                              </svg>
                            </div>
                          </div>
                          <div id="SHAPE3626" class='ladi-element'>
                            <div class='ladi-shape ladi-transition'>
                              <svg xmlns="http://www.w3.org/2000/svg" width="100%" height="100%" preserveAspectRatio="none" viewBox="0 0 1664 1896.08" fill="rgba(255, 188, 1, 1.0)">
                                <use xlink:href="#shape_pUOuupwEhH"></use>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div id="PARAGRAPH3627" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>Thích - Phản hồi</div>
                      </div>
                      <div id="IMAGE3628" class='ladi-element'>
                        <div class='ladi-image ladi-transition'>
                          <div class="ladi-image-background"></div>
                        </div>
                      </div>
                      <div id="PARAGRAPH3629" class='ladi-element'>
                        <div class='ladi-paragraph ladi-transition'>1</div>
                      </div>
                    </div>
                  </div>
                  <div id="PARAGRAPH3630" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>30 phút trước</div>
                  </div>
                </div>
              </div>
              <div id="GROUP3631" class='ladi-element'>
                <div class='ladi-group'>
                  <a href="https://www.facebook.com/GDN-Gia%CC%80y-Da-Bo%CC%80-Th%C3%A2%CC%A3t-111572890318415/" target="_blank" id="BOX3632" class='ladi-element'>
                    <div class='ladi-box ladi-transition'></div>
                  </a>
                  <div id="PARAGRAPH3633" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>Dạ, cam ơn anh Nam đã ủng hộ ạ <br>
                    </div>
                  </div>
                  <div id="PARAGRAPH3634" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>TMAXTECH</div>
                  </div>
                  <div id="PARAGRAPH3635" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>Phản hồi -</div>
                  </div>
                  <div id="PARAGRAPH3636" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'>35 phút trước</div>
                  </div>
                </div>
              </div>
              <div id="IMAGE3719" class='ladi-element'>
                <div class='ladi-image ladi-transition'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION3024" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="IMAGE3128" class='ladi-element'>
            <div class='ladi-image ladi-transition'>
              <div class="ladi-image-background"></div>
            </div>
          </div>
          <div id="GROUP3025" class='ladi-element'>
            <div class='ladi-group'>
              <div id="GROUP3026" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="BOX3027" class='ladi-element'>
                    <div class='ladi-box ladi-transition'></div>
                  </div>
                  <div id="GROUP3028" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="COUNTDOWN3029" class='ladi-element'>
                        <div class='ladi-countdown'>
                          <div id="COUNTDOWN_ITEM3030" class='ladi-element'>
                            <div class="ladi-countdown-background"></div>
                            <div class='ladi-countdown-text'>
                              <span>00</span>
                            </div>
                          </div>
                          <div id="COUNTDOWN_ITEM3031" class='ladi-element'>
                            <div class="ladi-countdown-background"></div>
                            <div class='ladi-countdown-text'>
                              <span>00</span>
                            </div>
                          </div>
                          <div id="COUNTDOWN_ITEM3032" class='ladi-element'>
                            <div class="ladi-countdown-background"></div>
                            <div class='ladi-countdown-text'>
                              <span>00</span>
                            </div>
                          </div>
                          <div id="COUNTDOWN_ITEM3033" class='ladi-element'>
                            <div class="ladi-countdown-background"></div>
                            <div class='ladi-countdown-text'>
                              <span>00</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div id="HEADLINE3034" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>Khuyến mãi kết thúc sau</h3>
                      </div>
                      <div id="HEADLINE3035" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>Ngày</h3>
                      </div>
                      <div id="HEADLINE3036" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>Giờ</h3>
                      </div>
                      <div id="HEADLINE3037" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>Phút</h3>
                      </div>
                      <div id="HEADLINE3038" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>Giây</h3>
                      </div>
                    </div>
                  </div>
                  <div id="FORM3039" data-config-id="646ee21640d03f00203e4215" class='ladi-element'>
                    <form autocomplete="off" method="post" class='ladi-form'>
                      <div id="BUTTON3040" class='ladi-element'>
                        <div class='ladi-button ladi-transition'>
                          <div class="ladi-button-background"></div>
                          <div id="BUTTON_TEXT3040" class='ladi-element ladi-button-headline'>
                            <p class='ladi-headline ladi-transition'>ĐĂNG KÝ ĐẶT HÀNG</p>
                          </div>
                        </div>
                      </div>
                      <div id="FORM_ITEM3042" class='ladi-element'>
                        <div class="ladi-form-item-container">
                          <div class="ladi-form-item-background"></div>
                          <div class='ladi-form-item'>
                            <input autocomplete="off" tabindex="1" name="name" required class="ladi-form-control" type="text" placeholder="Họ và tên" value="" />
                          </div>
                        </div>
                      </div>
                      <div id="FORM_ITEM3043" class='ladi-element'>
                        <div class="ladi-form-item-container">
                          <div class="ladi-form-item-background"></div>
                          <div class='ladi-form-item'>
                            <input autocomplete="off" tabindex="3" name="phone" required class="ladi-form-control" type="tel" placeholder="Số điện thoại" pattern="(\+84|0){1}(9|8|7|5|3){1}[0-9]{8}" value="" />
                          </div>
                        </div>
                      </div>
                      <div id="FORM_ITEM3044" class='ladi-element'>
                        <div class="ladi-form-item-container">
                          <div class="ladi-form-item-background"></div>
                          <div class='ladi-form-item ladi-form-checkbox ladi-form-checkbox-vertical'>
                            <div class="ladi-form-checkbox-box-item">
                              <div class="ladi-form-checkbox-item">
                                <input tabindex="4" name="form_item776" required type="radio" value="1 MÁY - GIÁ 1390K MIỄN SHIP" />
                                <span data-checked="false">1 MÁY - GIÁ 1390K MIỄN SHIP</span>
                              </div>
                              <div class="ladi-form-checkbox-item">
                                <input tabindex="4" name="form_item776" required type="radio" value="2 MÁY - GIÁ 2590K MIỄN SHIP" />
                                <span data-checked="false">2 MÁY - GIÁ 2590K MIỄN SHIP</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div id="FORM_ITEM3045" class='ladi-element'>
                        <div class="ladi-form-item-container">
                          <div class="ladi-form-item-background"></div>
                          <div class='ladi-form-item'>
                            <input autocomplete="off" tabindex="4" name="address" required class="ladi-form-control" type="text" placeholder="Địa chỉ" value="" />
                          </div>
                        </div>
                      </div>
                      <button type="submit" class="ladi-hidden"></button>
                    </form>
                  </div>
                  <div id="HEADLINE3046" class='ladi-element'>
                    <h3 class='ladi-headline ladi-transition'></h3>
                  </div>
                  <div id="PARAGRAPH3047" class='ladi-element'>
                    <div class='ladi-paragraph ladi-transition'></div>
                  </div>
                </div>
              </div>
              <div id="HEADLINE3048" class='ladi-element'>
                <h3 class='ladi-headline ladi-transition'>ĐẶT MUA NGAY</h3>
              </div>
            </div>
          </div>
          <div id="GROUP3483" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE3484" class='ladi-element'>
                <h2 class='ladi-headline ladi-transition'>Chỉ còn <span style="color: rgb(244, 67, 54);">08</span>&nbsp;lượt ưu đãi cuối cùng! </h2>
              </div>
              <div id="GROUP3485" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="GROUP3486" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="HEADLINE3487" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>
                          <span style="font-weight: normal;">Giá niêm yết :</span>&nbsp;
                        </h3>
                      </div>
                      <div id="HEADLINE3488" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>2.400.000Đ</h3>
                      </div>
                    </div>
                  </div>
                  <div id="GROUP3489" class='ladi-element'>
                    <div class='ladi-group'>
                      <div id="BOX3490" class='ladi-element'>
                        <div class='ladi-box ladi-transition'></div>
                      </div>
                      <div id="HEADLINE3491" class='ladi-element'>
                        <h3 class='ladi-headline ladi-transition'>Giá chỉ còn:&nbsp; <span style="color: rgb(255, 18, 1);">&nbsp;1.390.000Đ</span>
                        </h3>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP3726" class='ladi-element'>
            <div class='ladi-group'>
              <div id="LIST_PARAGRAPH3530" class='ladi-element'>
                <div class='ladi-list-paragraph ladi-transition'>
                  <ul>
                    <li>Phục vụ tận tâm, hỗ trợ khách hàng 24/7</li>
                    <li>Đặt chữ TÂM lên đầu trong kinh doanh</li>
                    <li>Khuyến khích kiểm tra hàng trước khi thanh toán&nbsp;</li>
                  </ul>
                </div>
              </div>
              <div id="BOX3531" class='ladi-element'>
                <div class='ladi-box ladi-transition'></div>
              </div>
              <div id="IMAGE3532" class='ladi-element'>
                <div class='ladi-image ladi-transition'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION216" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-overlay"></div>
        <div class="ladi-container">
          <div id="GROUP219" class='ladi-element'>
            <div class='ladi-group'>
              <div id="HEADLINE220" class='ladi-element'>
                <h4 class='ladi-headline ladi-transition'>MÁY HÀN ĐIỆN CẦM TAY</h4>
              </div>
              <div id="GROUP221" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="SHAPE222" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(0, 0, 0, 1.0)">
                        <path d="M6.62,10.79C8.06,13.62 10.38,15.94 13.21,17.38L15.41,15.18C15.69,14.9 16.08,14.82 16.43,14.93C17.55,15.3 18.75,15.5 20,15.5A1,1 0 0,1 21,16.5V20A1,1 0 0,1 20,21A17,17 0 0,1 3,4A1,1 0 0,1 4,3H7.5A1,1 0 0,1 8.5,4C8.5,5.25 8.7,6.45 9.07,7.57C9.18,7.92 9.1,8.31 8.82,8.59L6.62,10.79Z"></path>
                      </svg>
                    </div>
                  </div>
                  <div id="HEADLINE223" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>Hotline: 0982.872.123&nbsp; <br>
                    </p>
                  </div>
                </div>
              </div>
              <div id="GROUP224" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="SHAPE225" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(0, 0, 0, 1.0)">
                        <path d="M20,4H4A2,2 0 0,0 2,6V18A2,2 0 0,0 4,20H20A2,2 0 0,0 22,18V6A2,2 0 0,0 20,4M20,18H4V8L12,13L20,8V18M20,6L12,11L4,6V6H20V6Z"></path>
                      </svg>
                    </div>
                  </div>
                  <div id="HEADLINE226" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>https://www.cokhitmaxtech.shop/may-han-dien</p>
                  </div>
                </div>
              </div>
              <div id="GROUP227" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="SHAPE228" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%" viewBox="0 0 24 24" fill="rgba(0, 0, 0, 1.0)">
                        <path d="M10,20V14H14V20H19V12H22L12,3L2,12H5V20H10Z"></path>
                      </svg>
                    </div>
                  </div>
                  <div id="HEADLINE229" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>Địa chỉ: 43 Lưu Hữu Phước, Nam Từ Liêm, Hà Nội <br>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div id="GROUP856" class='ladi-element'>
            <div class='ladi-group'>
              <div id="LINE217" class='ladi-element'>
                <div class='ladi-line'>
                  <div class="ladi-line-container"></div>
                </div>
              </div>
              <div id="HEADLINE230" class='ladi-element'>
                <h4 class='ladi-headline ladi-transition'>DỊCH VỤ KHÁCH HÀNG</h4>
              </div>
              <a href="https://www.dhtgroup.online/dieu_khoan_dich_vu" target="_blank" id="GROUP231" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="HEADLINE232" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>Điều khoản dịch vụ</p>
                  </div>
                  <div id="SHAPE233" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" data-name="Layer 1" viewBox="0 0 100 100" x="0px" y="0px" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(0, 0, 0, 1.0)">
                        <path d="M98.85,61.58a4.85,4.85,0,0,1-3.52,5.89L40.13,81.36A9.78,9.78,0,1,1,29.35,76.9c.31-.08.63-.14.94-.19L17.06,24.11,2.78,12.44A4.85,4.85,0,1,1,8.92,4.93L24.44,17.62a4.85,4.85,0,0,1,1.63,2.57L39,71.64,93,58.06A4.85,4.85,0,0,1,98.85,61.58Zm-65.08-40a3.57,3.57,0,0,1,2.59-4.33l8.46-2.13,6.26-1.57L60,11.33l6.66-1.67,7.68-1.93a3.57,3.57,0,0,1,4.33,2.59l9.55,37.94a3.57,3.57,0,0,1-2.59,4.33L47.65,62.13a3.57,3.57,0,0,1-4.33-2.59ZM50.89,31.77c1,3.85,4.37,5.11,9,5.47,3.19.25,4.7.89,5.08,2.4s-.93,2.85-3.19,3.42a13.79,13.79,0,0,1-4.48.34,2.43,2.43,0,0,0-.8.06,2.32,2.32,0,0,0-1.77,2.23v.2a2.37,2.37,0,0,0,2.22,2.38,18.93,18.93,0,0,0,5-.35l.46,1.83a2,2,0,1,0,3.89-1l-.53-2.11c4.37-1.94,6.11-5.59,5.22-9.13s-3.36-5.28-8.51-5.76c-3.7-.42-5.3-.91-5.63-2.22-.28-1.11.27-2.44,2.86-3.08a12.59,12.59,0,0,1,3.64-.38A2.29,2.29,0,0,0,64,26a2.37,2.37,0,0,0-.55-4.67,17.49,17.49,0,0,0-3.76.39l-.39-1.55a2,2,0,0,0-3.89,1L55.82,23C51.78,24.91,50,28.27,50.89,31.77Z"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </a>
              <a href="https://www.dhtgroup.online/chinh_sach_van_chuyen" target="_blank" id="GROUP234" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="HEADLINE235" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>Giao hàng và vận chuyển</p>
                  </div>
                  <div id="SHAPE236" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 64 64" enable-background="new 0 0 64 64" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(0, 0, 0, 1.0)">
                        <g display="none">
                          <rect x="-306.495" y="-10.779" display="inline" width="787" height="375.042"></rect>
                        </g>
                        <g display="none">
                          <g display="inline">
                            <path d="M60,64H4c-2.2,0-4-1.8-4-4V4c0-2.2,1.8-4,4-4h56c2.2,0,4,1.8,4,4v56C64,62.2,62.2,64,60,64z"></path>
                            <line stroke="#FFFFFF" stroke-width="6" stroke-miterlimit="10" x1="12.5" y1="15.333" x2="51.5" y2="15.333"></line>
                            <line stroke="#FFFFFF" stroke-width="6" stroke-miterlimit="10" x1="12.5" y1="32" x2="51.5" y2="32"></line>
                            <line stroke="#FFFFFF" stroke-width="6" stroke-miterlimit="10" x1="12.5" y1="48.667" x2="51.5" y2="48.667"></line>
                          </g>
                        </g>
                        <g display="none">
                          <path display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" d="M59.127,62.969H4.873   C2.743,62.969,1,61.226,1,59.096V4.904c0-2.13,1.743-3.873,3.873-3.873h54.254c2.13,0,3.873,1.743,3.873,3.873v54.191   C63,61.226,61.257,62.969,59.127,62.969z"></path>
                          <rect x="13.109" y="12.967" display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" width="37.781" height="5.807"></rect>
                          <rect x="13.109" y="29.097" display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" width="37.781" height="5.807"></rect>
                          <rect x="13.109" y="45.227" display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" width="37.781" height="5.807"></rect>
                        </g>
                        <g>
                          <g>
                            <path d="M4.873,63.969h54.254c2.687,0,4.873-2.187,4.873-4.873V4.904c0-2.687-2.187-4.873-4.873-4.873H4.873    C2.187,0.031,0,2.217,0,4.904v54.191C0,61.782,2.187,63.969,4.873,63.969z M13.109,12.967h37.781v5.807H13.109V12.967z     M13.109,29.097h37.781v5.807H13.109V29.097z M13.109,45.227h37.781v5.807H13.109V45.227z"></path>
                          </g>
                        </g>
                        <g display="none">
                          <path display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" d="M59.127,62.969H4.873   C2.743,62.969,1,61.226,1,59.096V4.904c0-2.13,1.743-3.873,3.873-3.873h54.254c2.13,0,3.873,1.743,3.873,3.873v54.191   C63,61.226,61.257,62.969,59.127,62.969z"></path>
                          <rect x="13.109" y="12.967" display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" width="37.781" height="5.807"></rect>
                          <rect x="13.109" y="29.097" display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" width="37.781" height="5.807"></rect>
                          <rect x="13.109" y="45.227" display="inline" stroke="#000000" stroke-width="2" stroke-miterlimit="10" width="37.781" height="5.807"></rect>
                        </g>
                      </svg>
                    </div>
                  </div>
                </div>
              </a>
              <a href="https://www.dhtgroup.online/bao_mat_thong_tin" target="_blank" id="GROUP237" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="HEADLINE238" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>Bảo mật thông tin <br>
                    </p>
                  </div>
                  <div id="SHAPE239" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(0, 0, 0, 1.0)">
                        <g>
                          <rect x="-192" width="185" height="99"></rect>
                          <rect y="-36" width="100" height="30"></rect>
                          <text transform="matrix(1 0 0 1 66 -19.5)" font-family="'Helvetica'" font-size="2.4">http://thenounproject.com</text>
                          <text transform="matrix(1 0 0 1 7.166 -24.5)">
                            <tspan x="0" y="0" font-family="'Helvetica-Bold'" font-size="6.1578">The Noun Project</tspan>
                            <tspan x="1.12" y="4.8" font-family="'Helvetica-Bold'" font-size="4">Icon Template</tspan>
                          </text>
                          <text transform="matrix(1 0 0 1 -178.5 10.5)" font-family="'Helvetica-Bold'" font-size="6.1578">Reminders</text>
                          <line stroke="#FFFFFF" stroke-miterlimit="10" x1="8" y1="-14.5" x2="18" y2="-14.5"></line>
                          <line stroke="#FFFFFF" stroke-miterlimit="10" x1="-179" y1="16.5" x2="-162" y2="16.5"></line>
                          <g>
                            <g>
                              <g>
                                <rect x="-170.802" y="31.318" width="8.721" height="8.642"></rect>
                                <path d="M-164.455,42.312h4.747v-4.703h-4.747V42.312z M-159.266,42.749h-5.63V37.17h5.63V42.749      L-159.266,42.749z M-166.221,44.062h8.279v-8.203h-8.279V44.062L-166.221,44.062z M-157.5,44.5h-9.163v-9.079h9.163V44.5      L-157.5,44.5z"></path>
                                <polygon points="-166.149,44.133 -166.292,43.991 -158.013,35.787 -157.871,35.929     "></polygon>
                              </g>
                            </g>
                          </g>
                          <rect x="-179" y="58" width="35" height="32.5"></rect>
                          <text transform="matrix(1 0 0 1 -179 60.1572)">
                            <tspan x="0" y="0" font-family="'Helvetica-Bold'" font-size="3">Strokes</tspan>
                            <tspan x="0" y="5" font-family="'Helvetica'" font-size="2.4">Try to keep strokes at 4px</tspan>
                            <tspan x="0" y="10" font-family="'Helvetica'" font-size="2.4">Minimum stroke weight is 2px</tspan>
                            <tspan x="0" y="14.5" font-family="'Helvetica'" font-size="2.4">For thicker strokes use even </tspan>
                            <tspan x="0" y="17.5" font-family="'Helvetica'" font-size="2.4">numbers: 6px, 8px etc.</tspan>
                            <tspan x="0" y="22" font-family="'Helvetica-Bold'" font-size="2.4">Remember to expand strokes </tspan>
                            <tspan x="0" y="25" font-family="'Helvetica-Bold'" font-size="2.4">before saving as an SVG </tspan>
                          </text>
                          <rect x="-136.5" y="58" width="35" height="32.5"></rect>
                          <text transform="matrix(1 0 0 1 -136.5 60.1572)">
                            <tspan x="0" y="0" font-family="'Helvetica-Bold'" font-size="3">Size</tspan>
                            <tspan x="0" y="5" font-family="'Helvetica'" font-size="2.4">Cannot be wider or taller than </tspan>
                            <tspan x="0" y="8.5" font-family="'Helvetica'" font-size="2.4">100px (artboard size)</tspan>
                            <tspan x="0" y="13.5" font-family="'Helvetica'" font-size="2.4">Scale your icon to fill as much of </tspan>
                            <tspan x="0" y="16.5" font-family="'Helvetica'" font-size="2.4">the artboard as possible</tspan>
                          </text>
                          <rect x="-94" y="58" width="35" height="32.5"></rect>
                          <text transform="matrix(1 0 0 1 -94 60.1572)">
                            <tspan x="0" y="0" font-family="'Helvetica-Bold'" font-size="3">Ungroup</tspan>
                            <tspan x="0" y="5" font-family="'Helvetica'" font-size="2.4">If your design has more than one </tspan>
                            <tspan x="0" y="8" font-family="'Helvetica'" font-size="2.4">shape, make sure to ungroup</tspan>
                          </text>
                          <rect x="-50" y="58" width="35" height="32.5"></rect>
                          <text transform="matrix(1 0 0 1 -50 60.1572)">
                            <tspan x="0" y="0" font-family="'Helvetica-Bold'" font-size="3">Save as</tspan>
                            <tspan x="0" y="5" font-family="'Helvetica'" font-size="2.4">Save as .SVG and make sure </tspan>
                            <tspan x="0" y="8" font-family="'Helvetica'" font-size="2.4">“Use Artboards” is checked</tspan>
                          </text>
                          <text transform="matrix(1.0074 0 0 1 -125.542 30.5933)" font-family="'Helvetica'" font-size="2.5731">100px</text>
                          <text transform="matrix(1.0074 0 0 1 -41 39)" font-family="'Helvetica-Bold'" font-size="5.1462">.SVG</text>
                          <rect x="-126.514" y="34.815" width="10.261" height="10.185"></rect>
                          <rect x="-126.477" y="31.766" width="0.522" height="2.337"></rect>
                          <rect x="-116.812" y="31.766" width="0.523" height="2.337"></rect>
                          <rect x="-127" y="32.337" width="11.233" height="0.572"></rect>
                          <g>
                            <rect x="-83.805" y="33.844" width="10.305" height="10.156"></rect>
                            <rect x="-76.809" y="28.707" width="3.308" height="3.261"></rect>
                          </g>
                          <rect x="-178.5" y="22.5" stroke="#FFFFFF" stroke-miterlimit="10" width="30" height="30"></rect>
                          <rect x="-136.5" y="22.5" stroke="#FFFFFF" stroke-miterlimit="10" width="30" height="30"></rect>
                          <rect x="-93.5" y="22.5" stroke="#FFFFFF" stroke-miterlimit="10" width="30" height="30"></rect>
                          <rect x="-49.5" y="22.5" stroke="#FFFFFF" stroke-miterlimit="10" width="30" height="30"></rect>
                        </g>
                        <g>
                          <path d="M83.64,40H78v-9.208c0-15.24-12.76-27.638-28-27.638c-15.239,0-28,12.398-28,27.638V40h-6.217   C14.542,40,13,42.119,13,43.36v50.332C13,94.932,14.542,96,15.783,96H83.64c1.241,0,3.36-1.068,3.36-2.308V43.36   C87,42.119,84.881,40,83.64,40z M58.015,83.265C57.719,83.665,57.249,84,56.75,84H43.571c-0.5,0-0.969-0.335-1.266-0.735   c-0.296-0.403-0.384-0.97-0.238-1.448l4.511-14.673c-2.112-1.368-3.616-4.102-3.616-6.835c0-3.924,3.228-7.121,7.198-7.121   c3.901,0,7.198,3.37,7.198,7.363c0,2.698-1.452,5.236-3.607,6.552l4.502,14.717C58.399,82.296,58.312,82.862,58.015,83.265z M62,40   H38v-9.208c0-6.567,5.434-11.91,12-11.91c6.567,0,12,5.343,12,11.91V40z"></path>
                        </g>
                      </svg>
                    </div>
                  </div>
                </div>
              </a>
              <a href="https://www.dhtgroup.online/bao_hanh_ho_tro" target="_blank" id="GROUP240" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="HEADLINE241" class='ladi-element'>
                    <p class='ladi-headline ladi-transition'>Bảo hành và hỗ trợ <br>
                    </p>
                  </div>
                  <div id="SHAPE242" class='ladi-element'>
                    <div class='ladi-shape ladi-transition'>
                      <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" x="0px" y="0px" viewBox="0 0 100 100" enable-background="new 0 0 100 100" xml:space="preserve" preserveAspectRatio="none" width="100%" height="100%" class="" fill="rgba(0, 0, 0, 1.0)">
                        <path d="M74.048,30.427v34.746H72.5h-7.452v7.361l-5.18-5.18l-2.183-2.182H54.6H12.452V30.427H74.048 M81.5,22.976H5v49.649h49.6  l17.9,17.899V72.625h9V22.976L81.5,22.976z M61.879,44.074h-7.452v7.452h7.452V44.074z M46.976,44.074h-7.451v7.452h7.451V44.074z   M32.072,44.074H24.62v7.452h7.453V44.074z M18.5,9.476v7.451h7.452h61.596v34.746v7.452H95V9.476H18.5z"></path>
                      </svg>
                    </div>
                  </div>
                </div>
              </a>
            </div>
          </div>
          <div id="GROUP857" class='ladi-element'>
            <div class='ladi-group'>
              <div id="FRAME243" class='ladi-element'>
                <div class='ladi-frame ladi-transition'>
                  <div class="ladi-frame-background"></div>
                  <div id="IMAGE244" class='ladi-element'>
                    <div class='ladi-image ladi-transition'>
                      <div class="ladi-image-background"></div>
                    </div>
                  </div>
                  <div id="IMAGE245" class='ladi-element'>
                    <div class='ladi-image ladi-transition'>
                      <div class="ladi-image-background"></div>
                    </div>
                  </div>
                  <div id="IMAGE246" class='ladi-element'>
                    <div class='ladi-image ladi-transition'>
                      <div class="ladi-image-background"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div id="HEADLINE247" class='ladi-element'>
                <h1 class='ladi-headline ladi-transition'></h1>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div id="SECTION_POPUP" class='ladi-section'>
        <div class='ladi-section-background'></div>
        <div class="ladi-container">
          <div id="POPUP328" class='ladi-element'>
            <div class='ladi-popup'>
              <div class="ladi-popup-background"></div>
              <div id="IMAGE329" class='ladi-element'>
                <div class='ladi-image ladi-transition'>
                  <div class="ladi-image-background"></div>
                </div>
              </div>
              <div id="GROUP330" class='ladi-element'>
                <div class='ladi-group'>
                  <div id="BOX331" class='ladi-element'>
                    <div class='ladi-box ladi-transition'></div>
                  </div>
                  <div id="HEADLINE332" class='ladi-element'>
                    <h5 class='ladi-headline ladi-transition'>MUA THÀNH CÔNG</h5>
                  </div>
                </div>
              </div>
              <div id="PARAGRAPH333" class='ladi-element'>
                <div class='ladi-paragraph ladi-transition'>Cảm ơn quý khách đã mua hàng. <br>Chúng tôi sẽ liên hệ lại quý khách sớm nhất. <br>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div id="backdrop-popup" class="backdrop-popup"></div>
    <div id="backdrop-dropbox" class="backdrop-dropbox"></div>
    <div id="lightbox-screen" class="lightbox-screen"></div>
    <script id="script_lazyload" type="text/javascript">
      window.lazyload_run = function(dom, is_first, check_dom_rect) {
        if (check_dom_rect && (document.body.clientWidth <= 0 || document.body.clientheight <= 0)) {
          return setTimeout(function() {
            window.lazyload_run(dom, is_first, check_dom_rect);
          }, 1);
        }
        var style_lazyload = document.getElementById('style_lazyload');
        var list_element_lazyload = dom.querySelectorAll('.ladi-overlay, .ladi-box, .ladi-button-background, .ladi-collection-item, .ladi-countdown-background, .ladi-form-item-background, .ladi-form-label-container .ladi-form-label-item.image, .ladi-frame-background, .ladi-gallery-view-item, .ladi-gallery-control-item, .ladi-headline, .ladi-image-background, .ladi-image-compare, .ladi-list-paragraph ul li, .ladi-section-background, .ladi-survey-option-background, .ladi-survey-option-image, .ladi-tabs-background, .ladi-video-background, .ladi-banner, .ladi-spin-lucky-screen, .ladi-spin-lucky-start');
        var docEventScroll = window;
        for (var i = 0; i < list_element_lazyload.length; i++) {
          var rect = list_element_lazyload[i].getBoundingClientRect();
          if (rect.x == "undefined" || rect.x == undefined || rect.y == "undefined" || rect.y == undefined) {
            rect.x = rect.left;
            rect.y = rect.top;
          }
          var offset_top = rect.y + window.scrollY;
          if (offset_top >= window.scrollY + window.innerHeight || window.scrollY >= offset_top + list_element_lazyload[i].offsetHeight) {
            list_element_lazyload[i].classList.add('ladi-lazyload');
          }
        }
        if (typeof style_lazyload != "undefined" && style_lazyload != undefined) {
          style_lazyload.parentElement.removeChild(style_lazyload);
        }
        var currentScrollY = window.scrollY;
        var stopLazyload = function(event) {
          if (event.type == "scroll" && window.scrollY == currentScrollY) {
            currentScrollY = -1;
            return;
          }
          docEventScroll.removeEventListener('scroll', stopLazyload);
          list_element_lazyload = document.getElementsByClassName('ladi-lazyload');
          while (list_element_lazyload.length > 0) {
            list_element_lazyload[0].classList.remove('ladi-lazyload');
          }
        };
        if (is_first) {
          var scrollEventPassive = null;
          try {
            var opts = Object.defineProperty({}, 'passive', {
              get: function() {
                scrollEventPassive = {
                  passive: true
                };
              }
            });
            window.addEventListener('testPassive', null, opts);
            window.removeEventListener('testPassive', null, opts);
          } catch (e) {}
          docEventScroll.addEventListener('scroll', stopLazyload, scrollEventPassive);
        }
        return dom;
      };
      window.lazyload_run(document, true, true);
    </script>
    <!--[if lt IE 9]>
																															<script src="https://w.ladicdn.com/v2/source/html5shiv.min.js?v=1695116904561"></script>
																															<script src="https://w.ladicdn.com/v2/source/respond.min.js?v=1695116904561"></script>
																															<![endif]-->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&family=Dancing+Script:wght@400;700&family=Tinos:wght@400;700&family=Quicksand:wght@400;700&family=Montserrat:wght@400;700&family=Roboto:wght@400;700&family=Roboto+Slab:wght@400;700&family=Oswald:wght@400;700&display=swap" rel="stylesheet" type="text/css">
    <style type="text/css">
      @-webkit-keyframes bounceInLeft {
        0% {
          opacity: 0;
          -webkit-transform: translateX(-2000px);
          transform: translateX(-2000px);
        }

        60% {
          opacity: 1;
          -webkit-transform: translateX(30px);
          transform: translateX(30px);
        }

        80% {
          -webkit-transform: translateX(-10px);
          transform: translateX(-10px);
        }

        100% {
          -webkit-transform: translateX(0);
          transform: translateX(0);
        }
      }

      @keyframes bounceInLeft {
        0% {
          opacity: 0;
          -webkit-transform: translateX(-2000px);
          -ms-transform: translateX(-2000px);
          transform: translateX(-2000px);
        }

        60% {
          opacity: 1;
          -webkit-transform: translateX(30px);
          -ms-transform: translateX(30px);
          transform: translateX(30px);
        }

        80% {
          -webkit-transform: translateX(-10px);
          -ms-transform: translateX(-10px);
          transform: translateX(-10px);
        }

        100% {
          -webkit-transform: translateX(0);
          -ms-transform: translateX(0);
          transform: translateX(0);
        }
      }

      @-webkit-keyframes fadeInLeft {
        0% {
          opacity: 0;
          -webkit-transform: translateX(-20px);
          transform: translateX(-20px);
        }

        100% {
          opacity: 1;
          -webkit-transform: translateX(0);
          transform: translateX(0);
        }
      }

      @keyframes fadeInLeft {
        0% {
          opacity: 0;
          -webkit-transform: translateX(-20px);
          -ms-transform: translateX(-20px);
          transform: translateX(-20px);
        }

        100% {
          opacity: 1;
          -webkit-transform: translateX(0);
          -ms-transform: translateX(0);
          transform: translateX(0);
        }
      }

      @-webkit-keyframes pulse {
        0% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }

        50% {
          -webkit-transform: scale(1.1);
          transform: scale(1.1);
        }

        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      }

      @keyframes pulse {
        0% {
          -webkit-transform: scale(1);
          -ms-transform: scale(1);
          transform: scale(1);
        }

        50% {
          -webkit-transform: scale(1.1);
          -ms-transform: scale(1.1);
          transform: scale(1.1);
        }

        100% {
          -webkit-transform: scale(1);
          -ms-transform: scale(1);
          transform: scale(1);
        }
      }

      @-webkit-keyframes rubberBand {
        0% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }

        30% {
          -webkit-transform: scaleX(1.25) scaleY(0.75);
          transform: scaleX(1.25) scaleY(0.75);
        }

        40% {
          -webkit-transform: scaleX(0.75) scaleY(1.25);
          transform: scaleX(0.75) scaleY(1.25);
        }

        60% {
          -webkit-transform: scaleX(1.15) scaleY(0.85);
          transform: scaleX(1.15) scaleY(0.85);
        }

        100% {
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      }

      @keyframes rubberBand {
        0% {
          -webkit-transform: scale(1);
          -ms-transform: scale(1);
          transform: scale(1);
        }

        30% {
          -webkit-transform: scaleX(1.25) scaleY(0.75);
          -ms-transform: scaleX(1.25) scaleY(0.75);
          transform: scaleX(1.25) scaleY(0.75);
        }

        40% {
          -webkit-transform: scaleX(0.75) scaleY(1.25);
          -ms-transform: scaleX(0.75) scaleY(1.25);
          transform: scaleX(0.75) scaleY(1.25);
        }

        60% {
          -webkit-transform: scaleX(1.15) scaleY(0.85);
          -ms-transform: scaleX(1.15) scaleY(0.85);
          transform: scaleX(1.15) scaleY(0.85);
        }

        100% {
          -webkit-transform: scale(1);
          -ms-transform: scale(1);
          transform: scale(1);
        }
      }

      @-webkit-keyframes flash {

        0%,
        100%,
        50% {
          opacity: 1;
        }

        25%,
        75% {
          opacity: 0;
        }
      }

      @keyframes flash {

        0%,
        100%,
        50% {
          opacity: 1;
        }

        25%,
        75% {
          opacity: 0;
        }
      }

      .ladipage-animated-headline-duplicate {
        display: none;
      }

      .ladipage-animated-words-wrapper b,
      .ladipage-animated-words-wrapper i {
        font: inherit;
      }

      .ladipage-animated-words-wrapper,
      .ladipage-animated-words-wrapper b {
        display: inline-block;
      }

      .ladipage-animated-words-wrapper {
        position: relative;
        text-align: left;
      }

      .ladipage-animated-words-wrapper b {
        position: absolute;
        white-space: nowrap;
        left: 0;
        top: 0;
      }

      .ladipage-animated-words-wrapper b.is-visible {
        position: relative;
      }

      .no-js .ladipage-animated-words-wrapper b {
        opacity: 0;
      }

      .no-js .ladipage-animated-words-wrapper b.is-visible {
        opacity: 1;
      }

      .ladipage-animated-headline.clip span {
        display: inline-block;
      }

      .ladipage-animated-headline.clip .ladipage-animated-words-wrapper {
        vertical-align: top;
        -webkit-transition: width 0.6s;
        transition: width 0.6s;
        overflow: hidden;
      }

      .ladipage-animated-headline.clip .ladipage-animated-words-wrapper .after {
        position: absolute;
        top: 0;
        right: 0;
        width: 3px;
        height: 100%;
        background-color: #363636;
      }

      .ladipage-animated-headline.clip b {
        opacity: 0;
      }

      .ladipage-animated-headline.clip b.is-visible {
        opacity: 1;
      }
    </style>
    <script src="https://w.ladicdn.com/v2/source/ladipagev3.min.js?v=1695116904561" type="text/javascript"></script>
    <script id="script_event_data" type="application/json">
      {
        "GROUP231": {
          "a": "group",
          "cs": [{
            "dw": "https://www.dhtgroup.online/dieu_khoan_dich_vu",
            "dr": "action",
            "a": "link"
          }]
        },
        "GROUP234": {
          "a": "group",
          "cs": [{
            "dw": "https://www.dhtgroup.online/chinh_sach_van_chuyen",
            "dr": "action",
            "a": "link"
          }]
        },
        "GROUP237": {
          "a": "group",
          "cs": [{
            "dw": "https://www.dhtgroup.online/bao_mat_thong_tin",
            "dr": "action",
            "a": "link"
          }]
        },
        "GROUP240": {
          "a": "group",
          "cs": [{
            "dw": "https://www.dhtgroup.online/bao_hanh_ho_tro",
            "dr": "action",
            "a": "link"
          }]
        },
        "POPUP328": {
          "a": "popup",
          "X": "default",
          "U": "background-color: rgba(0, 0, 0, 0.5);"
        },
        "BUTTON1362": {
          "a": "button",
          "cs": [{
            "dw": "SECTION145",
            "dr": "action",
            "a": "section"
          }]
        },
        "GROUP2783": {
          "a": "group",
          "F": "bounceInLeft",
          "C": "1s"
        },
        "GROUP2790": {
          "a": "group",
          "F": "bounceInLeft",
          "C": "1s"
        },
        "GROUP2797": {
          "a": "group",
          "F": "bounceInLeft",
          "C": "1s"
        },
        "GROUP2804": {
          "a": "group",
          "F": "bounceInLeft",
          "C": "1s"
        },
        "GROUP3026": {
          "a": "group",
          "F": "fadeInLeft",
          "C": "0.2s"
        },
        "COUNTDOWN3029": {
          "a": "countdown",
          "bX": "countdown",
          "bW": 720
        },
        "COUNTDOWN_ITEM3030": {
          "a": "countdown_item",
          "bY": "day"
        },
        "COUNTDOWN_ITEM3031": {
          "a": "countdown_item",
          "bY": "hour"
        },
        "COUNTDOWN_ITEM3032": {
          "a": "countdown_item",
          "bY": "minute"
        },
        "COUNTDOWN_ITEM3033": {
          "a": "countdown_item",
          "bY": "seconds"
        },
        "FORM3039": {
          "a": "form",
          "bP": "646ee21640d03f00203e4215",
          "bM": true,
          "bK": "url",
          "bJ": "https://www.dhttools.com/cam-on-quy-khach",
          "bB": "fbq(&#39;track&#39;, &#39;CompleteRegistration&#39;, &#123;\nvalue: 1490000,\ncurrency: &#39;VND&#39;\n&#125;);\nfbq(&#39;track&#39;, &#39;Purchase&#39;,\n  &#123;\n    value: 1490000,\n    currency: &#39;VND&#39;,\n    content_ids: [&#39;mayhancamtay&#39;],\n    content_type: &#39;product&#39;\n  &#125;\n);\nfbq(&#39;track&#39;, &#39;CompleteRegistration_mayhancamtay&#39;, &#123;\nvalue: 1490000,\ncurrency: &#39;VND&#39;\n&#125;);",
          "bA": true,
          "by": true,
          "bx": true,
          "bw": true
        },
        "BUTTON3040": {
          "a": "button",
          "F": "pulse",
          "C": "1s"
        },
        "FORM_ITEM3042": {
          "a": "form_item",
          "bS": "text",
          "bQ": 1
        },
        "FORM_ITEM3043": {
          "a": "form_item",
          "bS": "tel",
          "bQ": 3
        },
        "FORM_ITEM3044": {
          "a": "form_item",
          "bS": "radio",
          "bQ": 4
        },
        "FORM_ITEM3045": {
          "a": "form_item",
          "bS": "text",
          "bQ": 4
        },
        "SECTION3293": {
          "a": "section",
          "aB": true,
          "ax": "top",
          "au": "0px",
          "ao": "0px"
        },
        "HEADLINE3294": {
          "a": "headline",
          "cs": [{
            "dw": "SECTION2853",
            "dr": "action",
            "a": "section"
          }]
        },
        "BUTTON3297": {
          "a": "button",
          "cs": [{
            "dr": "action",
            "dw": "SECTION3024",
            "a": "section"
          }],
          "F": "pulse",
          "C": "1s"
        },
        "HEADLINE3300": {
          "a": "headline",
          "cs": [{
            "dr": "action",
            "dw": "SECTION3335",
            "a": "section"
          }]
        },
        "HEADLINE3302": {
          "a": "headline",
          "cs": [{
            "dw": "SECTION3024",
            "dr": "action",
            "a": "section"
          }]
        },
        "HEADLINE3304": {
          "a": "headline",
          "cs": [{
            "dw": "SECTION3024",
            "dr": "action",
            "a": "section"
          }]
        },
        "IMAGE3334": {
          "a": "image",
          "D": "rubberBand",
          "A": "1s"
        },
        "BUTTON3325": {
          "a": "button",
          "cs": [{
            "dr": "action",
            "dw": "SECTION3024",
            "a": "section"
          }],
          "F": "pulse",
          "C": "0s"
        },
        "HEADLINE2539": {
          "a": "headline"
        },
        "HEADLINE3491": {
          "a": "headline",
          "F": "pulse",
          "C": "1s"
        },
        "GALLERY3549": {
          "a": "gallery",
          "ah": true,
          "ae": 4
        },
        "BOX3610": {
          "a": "box",
          "cs": [{
            "dw": "https://www.facebook.com/GDN-Gia%CC%80y-Da-Bo%CC%80-Th%C3%A2%CC%A3t-111572890318415/",
            "dr": "action",
            "a": "link"
          }]
        },
        "BOX3632": {
          "a": "box",
          "cs": [{
            "dw": "https://www.facebook.com/GDN-Gia%CC%80y-Da-Bo%CC%80-Th%C3%A2%CC%A3t-111572890318415/",
            "dr": "action",
            "a": "link"
          }]
        },
        "BOX3682": {
          "a": "box",
          "cs": [{
            "dw": "https://www.facebook.com/GDN-Gia%CC%80y-Da-Bo%CC%80-Th%C3%A2%CC%A3t-111572890318415/",
            "dr": "action",
            "a": "link"
          }]
        },
        "VIDEO3683": {
          "a": "video",
          "ci": "https://s.ladicdn.com/5dbe4694ed94dc587f3c244b/may-han-dien-cam-tay-25523-20230525031147-vcnil.mp4",
          "ch": "direct",
          "cg": true,
          "Y": true
        }
      }
    </script>
    <script id="script_ladipage_run" type="text/javascript">
      (function() {
        var run = function() {
          if (typeof window.LadiPageScript == "undefined" || typeof window.ladi == "undefined" || window.ladi == undefined) {
            setTimeout(run, 100);
            return;
          }
          window.LadiPageApp = window.LadiPageApp || new window.LadiPageAppV2();
          window.LadiPageScript.runtime.ladipage_id = '648676420651a900125c310d';
          window.LadiPageScript.runtime.publish_platform = 'LADIPAGEDNS';
          window.LadiPageScript.runtime.is_mobile_only = true;
          window.LadiPageScript.runtime.version = '1695116904561';
          window.LadiPageScript.runtime.cdn_url = 'https://w.ladicdn.com/v2/source/';
          window.LadiPageScript.runtime.DOMAIN_SET_COOKIE = ["cokhitmaxtech.shop"];
          window.LadiPageScript.runtime.DOMAIN_FREE = ["preview.ladipage.me", "ldp.link", "ldp.page"];
          window.LadiPageScript.runtime.bodyFontSize = 12;
          window.LadiPageScript.runtime.store_id = "5dbe4694ed94dc587f3c244b";
          window.LadiPageScript.runtime.time_zone = 7;
          window.LadiPageScript.runtime.currency = "USD";
          window.LadiPageScript.runtime.convert_replace_str = true;
          window.LadiPageScript.runtime.desktop_width = 960;
          window.LadiPageScript.runtime.mobile_width = 420;
          window.LadiPageScript.runtime.formdata = true;
          window.LadiPageScript.runtime.tracking_button_click = true;
          window.LadiPageScript.runtime.lang = "vi";
          window.LadiPageScript.run(true);
          window.LadiPageScript.runEventScroll();
        };
        run();
      })();
    </script>
  </body>
</html>
<!--Publish time: Thu, 21 Sep 2023 02:49:16 GMT-->
<!--LadiPage build time: Tue, 19 Sep 2023 09:48:24 GMT-->